// DODO was here

import {
  ClientErrorObject,
  getClientErrorObject,
  SupersetClient,
  t,
} from '@superset-ui/core';
import Tag from 'src/types/TagType';

import rison from 'rison';
import { cacheWrapper } from 'src/utils/cacheWrapper';

const localCache = new Map<string, any>();

const cachedSupersetGet = cacheWrapper(
  SupersetClient.get,
  localCache,
  ({ endpoint }) => endpoint || '',
);

type SelectTagsValue = {
  value: number | undefined;
  label: string | undefined;
  key: number | undefined;
};

export const tagToSelectOption = (
  tag: Tag & { table_name: string },
): SelectTagsValue => ({
  value: tag.id,
  label: tag.name,
  key: tag.id,
});

export const loadTags = async (
  search: string,
  page: number,
  pageSize: number,
  withTeamTags = false, // DODO added 52010498
) => {
  const searchColumn = 'name';
  const query = rison.encode({
    filters: [
      { col: searchColumn, opr: 'ct', value: search },
      // DODO changed 52010498
      // { col: 'type', opr: 'custom_tag', value: true }, // DODO commented out 52010498
      { col: 'type', opr: 'custom_with_team_tag', value: withTeamTags }, // DODO added 52010498
    ],
    page,
    page_size: pageSize,
    order_column: searchColumn,
    order_direction: 'asc',
  });

  const getErrorMessage = ({ error, message }: ClientErrorObject) => {
    let errorText = message || error || t('An error has occurred');
    if (message === 'Forbidden') {
      errorText = t('You do not have permission to read tags');
    }
    return errorText;
  };

  return cachedSupersetGet({
    endpoint: `/api/v1/tag/?q=${query}`,
  })
    .then(response => {
      const data: {
        label: string;
        value: string | number;
      }[] = response.json.result.map(tagToSelectOption);
      return {
        data,
        totalCount: response.json.count,
      };
    })
    .catch(async error => {
      const errorMessage = getErrorMessage(await getClientErrorObject(error));
      throw new Error(errorMessage);
    });
};
