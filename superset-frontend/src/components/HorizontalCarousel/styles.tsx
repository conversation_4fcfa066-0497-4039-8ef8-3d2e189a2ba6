// DODO was here
// Team Carousel UI On Home page #52010498

import { styled } from '@superset-ui/core';

const CarouselContent = styled.div<{ padding: number }>`
  ${({ theme, padding }) => `
    overflow-x: auto;
    padding: 0 ${padding}px;

    &::-webkit-scrollbar {
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: ${theme.colors.grayscale.light4};
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: ${theme.colors.grayscale.light1};
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: ${theme.colors.grayscale.base};
    }
  `}
`;

const CarouselGrid = styled.div<{ itemWidth: number; gap: number }>`
  ${({ theme, itemWidth, gap }) => `
    display: flex;
    gap: ${gap}px;
    padding: 8px 0 30px 0;
    min-width: fit-content;

    > * {
      flex: 0 0 ${itemWidth}px;
    }
  `}
`;

export { CarouselContent, CarouselGrid };
