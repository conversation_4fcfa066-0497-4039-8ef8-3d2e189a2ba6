// DODO was here
// Team Carousel UI On Home page #52010498

import { ReactNode } from 'react';
import { CarouselContent, CarouselGrid } from './styles';

export interface HorizontalCarouselProps {
  children: ReactNode;
  itemWidth?: number;
  gap?: number;
  padding?: number;
}

const HorizontalCarousel = ({
  children,
  itemWidth = 320,
  gap = 16,
  padding = 16,
}: HorizontalCarouselProps) => (
  <CarouselContent padding={padding}>
    <CarouselGrid itemWidth={itemWidth} gap={gap}>
      {children}
    </CarouselGrid>
  </CarouselContent>
);

export default HorizontalCarousel;
