// DODO was here
import { useEffect, useState } from 'react';
import { styled, t, css, SupersetTheme } from '@superset-ui/core';
import { NumberParam, useQueryParam } from 'use-query-params';
import AllEntitiesTable, {
  TaggedObjects,
} from 'src/features/allEntities/AllEntitiesTable';
import Button from 'src/components/Button';
import MetadataBar, {
  MetadataType,
  Description,
  LastModified,
  Owner,
  ContentType,
} from 'src/components/MetadataBar';
import OwnerType from 'src/types/Owner';
import { PageHeaderWithActions } from 'src/components/PageHeaderWithActions';
import { Tag } from 'src/views/CRUD/types';
import TagModal from 'src/features/tags/TagModal';
import withToasts, { useToasts } from 'src/components/MessageToasts/withToasts';
import { fetchObjectsByTagIds, fetchSingleTag } from 'src/features/tags/tags';
import Loading from 'src/components/Loading';
import getOwnerName from 'src/utils/getOwnerName';

interface TaggedObjectDodoExtended {
  name_ru?: string; // DODO added 52010498
}
interface TaggedObject extends TaggedObjectDodoExtended {
  id: number;
  type: string;
  name: string;
  url: string;
  changed_on: moment.MomentInput;
  created_by: number | undefined;
  creator: string;
  owners: OwnerType[];
  tags: Tag[];
}

const additionalItemsStyles = (theme: SupersetTheme) => css`
  display: flex;
  align-items: center;
  margin-left: ${theme.gridUnit}px;
  & > span {
    margin-right: ${theme.gridUnit * 3}px;
  }
`;

const AllEntitiesContainer = styled.div`
  ${({ theme }) => `
  background-color: ${theme.colors.grayscale.light4};
  .select-control {
    margin-left: ${theme.gridUnit * 4}px;
    margin-right: ${theme.gridUnit * 4}px;
    margin-bottom: ${theme.gridUnit * 2}px;
  }
  .select-control-label {
    text-transform: uppercase;
    font-size: ${theme.gridUnit * 3}px;
    color: ${theme.colors.grayscale.base};
    margin-bottom: ${theme.gridUnit * 1}px;
  }
  .entities {
    margin: ${theme.gridUnit * 6}px; 0px;
  }
  .pagination-container {
    background-color: transparent;
  }
  `}
`;

const AllEntitiesNav = styled.div`
  ${({ theme }) => `
  height: ${theme.gridUnit * 12.5}px;
  background-color: ${theme.colors.grayscale.light5};
  margin-bottom: ${theme.gridUnit * 4}px;
  .navbar-brand {
    margin-left: ${theme.gridUnit * 2}px;
    font-weight: ${theme.typography.weights.bold};
  }
  .header {
    font-weight: ${theme.typography.weights.bold};
    margin-right:  ${theme.gridUnit * 3}px;
    text-align: left;
    font-size: ${theme.gridUnit * 4.5}px;
    padding: ${theme.gridUnit * 3}px;
    display: inline-block;
    line-height: ${theme.gridUnit * 9}px;
  }
  `};
`;

function AllEntities() {
  const [tagId] = useQueryParam('id', NumberParam);
  const [tag, setTag] = useState<Tag | null>(null);
  const [showTagModal, setShowTagModal] = useState<boolean>(false);
  const { addSuccessToast, addDangerToast } = useToasts();
  const [isLoading, setLoading] = useState<boolean>(false);
  const [objects, setObjects] = useState<TaggedObjects>({
    dashboard: [],
    chart: [],
    query: [],
  });

  const editableTitleProps = {
    title: tag?.name || '',
    placeholder: 'testing',
    onSave: (newDatasetName: string) => {},
    canEdit: false,
    label: t('dataset name'),
  };

  const items: ContentType[] = [];
  if (tag?.description) {
    const description: Description = {
      type: MetadataType.Description,
      value: tag?.description || '',
    };
    items.push(description);
  }

  const owner: Owner = {
    type: MetadataType.Owner,
    createdBy: getOwnerName(tag?.created_by),
    createdOn: tag?.created_on_delta_humanized || '',
  };
  items.push(owner);

  const lastModified: LastModified = {
    type: MetadataType.LastModified,
    value: tag?.changed_on_delta_humanized || '',
    modifiedBy: getOwnerName(tag?.changed_by),
  };
  items.push(lastModified);

  const fetchTaggedObjects = () => {
    setLoading(true);
    if (!tag) {
      addDangerToast('Error tag object is not referenced!');
      return;
    }
    fetchObjectsByTagIds(
      // DODO added start 52010498
      { tagIds: tag?.id ? [tag.id] : [], types: null },
      (data: TaggedObject[]) => {
        const objects: TaggedObjects = { dashboard: [], chart: [], query: [] };
        data.forEach(function (object) {
          const object_type = object.type as keyof TaggedObjects;
          if (object_type in objects) {
            objects[object_type].push(object);
          }
        });
        // DODO added stop 52010498
        setObjects(objects);
        setLoading(false);
      },
      () => {
        addDangerToast('Error Fetching Tagged Objects');
        setLoading(false);
      },
    );
  };

  const fetchTag = (tagId: number) => {
    fetchSingleTag(
      tagId,
      (tag: Tag) => {
        setTag(tag);
        setLoading(false);
      },
      () => {
        addDangerToast(t('Error Fetching Tagged Objects'));
        setLoading(false);
      },
    );
  };

  useEffect(() => {
    // fetch single tag met
    if (tagId) {
      setLoading(true);
      fetchTag(tagId);
    }
  }, [tagId]);

  useEffect(() => {
    if (tag) fetchTaggedObjects();
  }, [tag]);

  if (isLoading) return <Loading />;
  return (
    <AllEntitiesContainer>
      <TagModal
        show={showTagModal}
        onHide={() => {
          setShowTagModal(false);
        }}
        editTag={tag}
        addSuccessToast={addSuccessToast}
        addDangerToast={addDangerToast}
        refreshData={() => {
          fetchTaggedObjects();
          if (tagId) fetchTag(tagId);
        }}
        showAllInputs={tag?.type !== 5} // DODO added 52010498
      />
      <AllEntitiesNav>
        <PageHeaderWithActions
          additionalActionsMenu={<></>}
          editableTitleProps={editableTitleProps}
          faveStarProps={{ itemId: 1, saveFaveStar: () => {} }}
          showFaveStar={false}
          showTitlePanelItems
          titlePanelAdditionalItems={
            <div css={additionalItemsStyles}>
              <MetadataBar items={items} tooltipPlacement="bottom" />
            </div>
          }
          rightPanelAdditionalItems={
            <>
              <Button
                data-test="bulk-select-action"
                buttonStyle="secondary"
                onClick={() => setShowTagModal(true)}
                showMarginRight={false}
              >
                {t('Edit Tag')}{' '}
              </Button>
            </>
          }
          menuDropdownProps={{
            disabled: true,
          }}
          showMenuDropdown={false}
        />
      </AllEntitiesNav>
      <div className="entities">
        <AllEntitiesTable setShowTagModal={setShowTagModal} objects={objects} />
      </div>
    </AllEntitiesContainer>
  );
}

export default withToasts(AllEntities);
