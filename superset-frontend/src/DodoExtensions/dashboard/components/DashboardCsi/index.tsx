import { useState, useRef, lazy, Suspense } from 'react';
import { styled, t, SupersetClient } from '@superset-ui/core';
import { useToasts } from 'src/components/MessageToasts/withToasts';
import ModalTrigger, { ModalTriggerRef } from 'src/components/ModalTrigger';
import Button from 'src/components/Button';
import { bootstrapData } from 'src/preamble';
import Tabs from 'src/components/Tabs/Tabs';
import Checkbox from 'src/components/Checkbox';
import Loading from 'src/components/Loading';
import Table, { TableSize } from 'src/components/Table';
import { Select } from 'src/components';
import { EmptyStateSmall } from 'src/components/EmptyState';
import {
  getChartOptions,
  getTableColumns,
  Feedback,
  Granularity,
} from './utils';
import Summary from './Summary';

const EchartLazy = lazy(
  () => import('src/../plugins/plugin-chart-echarts/src/components/Echart'),
);

const locale = bootstrapData?.common?.locale || 'en';

const TabPane = styled(Tabs.TabPane)`
  position: relative;
  padding: ${({ theme }) => theme.gridUnit * 4}px 0px;
  min-height: 108px;
`;

const StyledSelect = styled(Select)`
  width: 110px;
`;

const Label = styled.p`
  margin: 0;
  text-wrap: nowrap;
`;

const ChartContainer = styled.div`
  margin-top: ${({ theme }) => theme.gridUnit * 4}px;
  min-height: 200px;
`;

const FlexRow = styled.div<{
  gapUnits?: number;
  justify?: string;
  align?: string;
}>`
  display: flex;
  justify-content: ${({ justify }) => justify || 'space-between'};
  align-items: ${({ align }) => align || 'center'};
  gap: ${({ gapUnits = 4, theme }) => theme.gridUnit * gapUnits}px;
`;

enum Env {
  Standalone = 'standaloneEnabled',
  Plugin = 'pluginEnabled',
}

type Tab = 'config' | 'data';

interface CSIConfig {
  standaloneEnabled: boolean;
  pluginEnabled: boolean;
}

const defaultConfig: CSIConfig = {
  standaloneEnabled: false,
  pluginEnabled: false,
};

const DashboardCsi = ({
  dashboardId,
  dashboardTitle,
  dashboardTitleRU,
}: {
  dashboardId: number;
  dashboardTitle: string;
  dashboardTitleRU: string;
}) => {
  const modalRef = useRef<ModalTriggerRef['current']>(null);
  const { addDangerToast, addSuccessToast } = useToasts();
  const [activeTab, setActiveTab] = useState<Tab>('config');
  const [isLoading, setIsLoading] = useState(false);

  // configuration states
  const [isApplying, setIsApplying] = useState(false);
  const [csiConfig, setCsiConfig] = useState<CSIConfig | null>(null);
  const [newConfig, setNewConfig] = useState<CSIConfig>(defaultConfig);

  // charts states
  const [csiData, setCsiData] = useState<Feedback[] | null>(null);
  const [isStacked, setIsStacked] = useState(true);
  const [granularity, setGranularity] = useState<Granularity>('month');

  const localisedDashboardTitle =
    locale === 'ru' ? dashboardTitleRU : dashboardTitle;

  const handleClose = () => {
    modalRef.current?.close();
  };

  const handleChangeTab = async (key: Tab) => {
    setActiveTab(key);

    if (key === 'data' && !csiData) {
      try {
        setIsLoading(true);
        // Get feedback data for this dashboard
        const feedbackResponse = await SupersetClient.get({
          endpoint: `/api/v1/csi_feedback/?dashboard_id=${dashboardId}`,
        });

        const feedbackData = feedbackResponse.json.result || [];

        setCsiData(feedbackData);
      } catch {
        addDangerToast(t('Failed to fetch dashboard CSI data.'));
      } finally {
        setIsLoading(false);
      }
    }
    if (key === 'config' && isLoading) setIsLoading(false);
  };

  const getCsiConfig = async () => {
    setIsLoading(true);
    try {
      // Get CSI configuration for this dashboard
      const configResponse = await SupersetClient.get({
        endpoint: `/api/v1/csi_config/${dashboardId}`,
      });

      const configData = configResponse.json;

      const config = {
        standaloneEnabled: configData.standalone_enabled,
        pluginEnabled: configData.plugin_enabled,
      };

      setCsiConfig(config);
      setNewConfig(config);
    } catch {
      addDangerToast(t('Failed to fetch dashboard CSI data.'));
    } finally {
      setIsLoading(false);
    }
  };

  const applyConfiguration = async () => {
    try {
      setIsApplying(true);

      await SupersetClient.put({
        endpoint: `/api/v1/csi_config/${dashboardId}`,
        jsonPayload: {
          standalone_enabled: newConfig.standaloneEnabled,
          plugin_enabled: newConfig.pluginEnabled,
        },
      });

      addSuccessToast(
        t(
          'CSI configuration updated for dashboard %s',
          localisedDashboardTitle,
        ),
      );

      handleClose();
    } catch (error) {
      addDangerToast(
        t(
          'An error occurred while updating CSI configuration for dashboard %s. Please try again.',
          localisedDashboardTitle,
        ),
      );
    } finally {
      setIsApplying(false);
    }
  };

  const handleEnvChange = (env: Env) => (checked: boolean) => {
    setNewConfig(prev => ({ ...prev, [env]: checked }));
  };

  const body = (
    <Tabs activeKey={activeTab} onChange={handleChangeTab}>
      <TabPane key="config" tab={t('Configuration')}>
        {isLoading && <Loading />}
        {!isLoading && csiConfig && (
          <>
            <p>{t('Set up feedback collection for:')}</p>
            <FlexRow justify="flex-start" gapUnits={2}>
              <Checkbox
                checked={newConfig.standaloneEnabled}
                onChange={handleEnvChange(Env.Standalone)}
              />
              <Label>Standalone</Label>
            </FlexRow>
            <FlexRow justify="flex-start" gapUnits={2}>
              <Checkbox
                checked={newConfig.pluginEnabled}
                onChange={handleEnvChange(Env.Plugin)}
              />
              <Label>Office Manager</Label>
            </FlexRow>
          </>
        )}
      </TabPane>

      <TabPane key="data" tab={t('Data')}>
        {isLoading && <Loading />}
        {!isLoading && csiData && <Summary dashboardId={dashboardId} />}
        {!isLoading && csiData && (
          <Tabs>
            <Tabs.TabPane key="table" tab={t('Table')}>
              <ChartContainer>
                <Table
                  data={csiData}
                  columns={getTableColumns()}
                  size={TableSize.Small}
                  usePagination
                  defaultPageSize={10}
                />
              </ChartContainer>
            </Tabs.TabPane>

            <Tabs.TabPane key="chart" tab={t('Chart')}>
              <FlexRow gapUnits={5}>
                <FlexRow gapUnits={2}>
                  <Checkbox
                    checked={isStacked}
                    onChange={val => setIsStacked(Boolean(val))}
                  />
                  <Label>{t('Stacked')}</Label>
                </FlexRow>
                <StyledSelect
                  value={granularity}
                  onChange={val => setGranularity(val as Granularity)}
                  options={[
                    { label: t('Day'), value: 'day' },
                    { label: t('Week'), value: 'week' },
                    { label: t('Month'), value: 'month' },
                  ]}
                />
              </FlexRow>
              {csiData.length > 0 && (
                <ChartContainer>
                  <Suspense fallback={<Loading />}>
                    <EchartLazy
                      {...getChartOptions(csiData, granularity, isStacked)}
                    />
                  </Suspense>
                </ChartContainer>
              )}
              {csiData.length === 0 && <EmptyStateSmall title={t('No data')} />}
            </Tabs.TabPane>
          </Tabs>
        )}
      </TabPane>
    </Tabs>
  );

  return (
    <ModalTrigger
      ref={modalRef}
      triggerNode={<span>{t('Dashboard CSI')}</span>}
      modalTitle={t('Dashboard CSI')}
      width="700px"
      responsive
      beforeOpen={getCsiConfig}
      onExit={() => {
        setActiveTab('config');
        setIsLoading(false);
        setIsApplying(false);
        setCsiConfig(null);
        setNewConfig(defaultConfig);
        setCsiData(null);
        setIsStacked(true);
        setGranularity('month');
      }}
      modalBody={body}
      modalFooter={
        <>
          {activeTab === 'config' && csiConfig && (
            <Button
              buttonStyle="primary"
              buttonSize="small"
              onClick={applyConfiguration}
              disabled={
                isApplying ||
                (csiConfig.standaloneEnabled === newConfig.standaloneEnabled &&
                  csiConfig.pluginEnabled === newConfig.pluginEnabled)
              }
              loading={isApplying}
            >
              {t('Apply')}
            </Button>
          )}
          <Button
            onClick={handleClose}
            buttonSize="small"
            disabled={isApplying}
          >
            {t('Close')}
          </Button>
        </>
      }
    />
  );
};

export default DashboardCsi;
