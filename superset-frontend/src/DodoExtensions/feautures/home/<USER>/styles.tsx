// DODO was here
import { Link } from 'react-router-dom';
import { styled } from '@superset-ui/core';
import { EntityType } from 'src/DodoExtensions/feautures/home/<USER>/types';

const StyledEntitySection = styled.div`
  ${({ theme }) => `
    margin-bottom: ${theme.gridUnit * 8}px;

    &:last-child {
      margin-bottom: 0;
    }
  `}
`;

const SectionHeader = styled.div<{ entityType: EntityType }>`
  ${({ theme, entityType }) => `
    display: flex;
    align-items: center;
    gap: ${theme.gridUnit * 3}px;
    margin-bottom: ${theme.gridUnit * 4}px;
    padding: 0 ${theme.gridUnit * 5}px;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: ${theme.gridUnit * 3}px;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 32px;
      border-radius: 2px;
      background: ${
        entityType === 'chart'
          ? theme.colors.primary.base
          : entityType === 'dashboard'
            ? theme.colors.secondary.base
            : theme.colors.grayscale.base
      };
    }
  `}
`;

const SectionIconWrapper = styled.div<{ entityType: EntityType }>`
  ${({ theme, entityType }) => `
    width: 48px;
    height: 48px;
    border-radius: ${theme.gridUnit * 2}px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: ${
      entityType === 'chart'
        ? theme.colors.primary.light4
        : entityType === 'dashboard'
          ? theme.colors.secondary.light4
          : theme.colors.grayscale.light3
    };
    color: ${
      entityType === 'chart'
        ? theme.colors.primary.dark1
        : entityType === 'dashboard'
          ? theme.colors.secondary.dark1
          : theme.colors.grayscale.dark1
    };
  `}
`;

const SectionTitleGroup = styled.div`
  flex: 1;
  min-width: 0;
`;

const SectionTitle = styled.h3`
  ${({ theme }) => `
    margin: 0 0 ${theme.gridUnit}px 0;
    font-size: ${theme.typography.sizes.l}px;
    font-weight: ${theme.typography.weights.bold};
    color: ${theme.colors.grayscale.dark2};
  `}
`;

const SectionSubtitle = styled.p`
  ${({ theme }) => `
    margin: 0;
    font-size: ${theme.typography.sizes.s}px;
    color: ${theme.colors.grayscale.base};
  `}
`;

const EmptySection = styled.div`
  ${({ theme }) => `
    padding: ${theme.gridUnit * 8}px ${theme.gridUnit * 5}px;
    text-align: center;
    color: ${theme.colors.grayscale.light1};
    font-style: italic;
  `}
`;

// Show All button as 5th item
const ShowAllCard = styled.div<{ entityType: EntityType }>`
  ${({ theme, entityType }) => `
    width: 150px;
    height: auto;
    padding: 10px 20px;
    margin-left: 20px;
    border-radius: ${theme.gridUnit * 2}px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    background: ${theme.colors.grayscale.light5};
    text-decoration: none;

    &:hover {
      border-color: ${
        entityType === 'chart'
          ? theme.colors.primary.base
          : entityType === 'dashboard'
            ? theme.colors.secondary.base
            : theme.colors.grayscale.base
      };
      background: ${
        entityType === 'chart'
          ? theme.colors.primary.light5
          : entityType === 'dashboard'
            ? theme.colors.secondary.light5
            : theme.colors.grayscale.light4
      };
      transform: translateX(2px);
      box-shadow: 0 4px 12px ${theme.colors.grayscale.dark2}1A;
      text-decoration: none;
    }
  `}
`;

const ShowAllText = styled.div`
  ${({ theme }) => `
    font-size: ${theme.typography.sizes.s}px;
    font-weight: ${theme.typography.weights.medium};
    color: ${theme.colors.grayscale.dark1};
    text-align: center;
    margin-bottom: ${theme.gridUnit * 0.5}px;
    text-decoration: none;
  `}
`;
const ShowAllLink = styled(Link)`
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: flex-start;

  &:hover {
    text-decoration: none;
  }

  &:focus {
    text-decoration: none;
  }

  &:visited {
    text-decoration: none;
  }
`;

export {
  StyledEntitySection,
  SectionHeader,
  SectionIconWrapper,
  SectionTitleGroup,
  SectionTitle,
  SectionSubtitle,
  EmptySection,
  ShowAllCard,
  ShowAllText,
  ShowAllLink,
};
