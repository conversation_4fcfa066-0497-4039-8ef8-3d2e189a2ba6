// DODO was here

import { styled } from '@superset-ui/core';
import { EntityType } from './types';

const StyledEntityCard = styled.div<{ entityType: EntityType }>`
  ${({ theme, entityType }) => `
    background: ${theme.colors.grayscale.light5};
    border: 1px solid ${theme.colors.grayscale.light2};
    border-radius: ${theme.gridUnit * 2}px;
    overflow: hidden;
    transition: all ${theme.transitionTiming}s ease-in-out;
    cursor: pointer;
    max-height: 280px;
    height: auto;
    display: flex;
    flex-direction: column;
    position: relative;



    &:hover {
      box-shadow: 0 8px 24px ${theme.colors.grayscale.dark2}1F;
      transform: translateY(-2px);
      border-color: ${
        entityType === 'chart'
          ? theme.colors.primary.base
          : entityType === 'dashboard'
            ? theme.colors.secondary.base
            : theme.colors.grayscale.base
      };
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: ${
        entityType === 'chart'
          ? `linear-gradient(90deg, ${theme.colors.primary.base}, ${theme.colors.primary.light1})`
          : entityType === 'dashboard'
            ? `linear-gradient(90deg, ${theme.colors.secondary.base}, ${theme.colors.secondary.light1})`
            : `linear-gradient(90deg, ${theme.colors.grayscale.base}, ${theme.colors.grayscale.light1})`
      };
    }
  `}
`;

const CardHeader = styled.div<{ entityType: EntityType }>`
  ${({ theme, entityType }) => `
    padding: ${theme.gridUnit * 4}px;
    background: ${
      entityType === 'chart'
        ? theme.colors.primary.light5
        : entityType === 'dashboard'
          ? theme.colors.secondary.light5
          : theme.colors.grayscale.light4
    };
    border-bottom: 1px solid ${theme.colors.grayscale.light2};
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
    min-height: 72px;
  `}
`;

const TitleContainer = styled.div`
  ${({ theme }) => `
    display: flex;
    flex-direction: column;
    gap: ${theme.gridUnit}px;
    flex: 1;
    min-width: 0;
  `}
`;

const TitleRow = styled.div`
  ${({ theme }) => `
    display: flex;
    align-items: center;
    gap: ${theme.gridUnit}px;
    min-width: 0;
  `}
`;

const TitleText = styled.span`
  ${({ theme }) => `
    font-size: ${theme.typography.sizes.m}px;
    font-weight: ${theme.typography.weights.medium};
    color: ${theme.colors.grayscale.dark2};
    line-height: 1.2;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    flex: 1;
    min-width: 0;
  `}
`;

const LanguageFlag = styled.span`
  ${({ theme }) => `
    font-size: ${theme.typography.sizes.xs}px;
    font-weight: ${theme.typography.weights.bold};
    color: ${theme.colors.grayscale.base};
    background: ${theme.colors.grayscale.light3};
    padding: ${theme.gridUnit / 2}px ${theme.gridUnit}px;
    border-radius: ${theme.gridUnit / 2}px;
    border: 1px solid ${theme.colors.grayscale.light2};
    flex-shrink: 0;
    text-transform: uppercase;
  `}
`;

const EntityIdContainer = styled.div`
  ${({ theme }) => `
    display: flex;
    align-items: center;
    gap: ${theme.gridUnit}px;
  `}
`;

const EntityIdTag = styled.div`
  ${({ theme }) => `
    display: inline-flex;
    align-items: center;
    gap: ${theme.gridUnit}px;
    padding: ${theme.gridUnit}px ${theme.gridUnit * 2}px;
    border-radius: ${theme.gridUnit}px;
    font-size: ${theme.typography.sizes.xs}px;
    font-weight: ${theme.typography.weights.bold};
    background: ${theme.colors.grayscale.light3};
    color: ${theme.colors.grayscale.dark2};
    border: 1px solid ${theme.colors.grayscale.light2};
  `}
`;

const CopyButton = styled.button`
  ${({ theme }) => `
    display: flex;
    align-items: center;
    justify-content: center;
    width: ${theme.gridUnit * 6}px;
    height: ${theme.gridUnit * 6}px;
    border: 1px solid ${theme.colors.grayscale.light2};
    border-radius: ${theme.gridUnit}px;
    background: ${theme.colors.grayscale.light5};
    color: ${theme.colors.grayscale.base};
    cursor: pointer;
    transition: all ${theme.transitionTiming}s ease;

    &:hover {
      background: ${theme.colors.grayscale.light4};
      color: ${theme.colors.primary.base};
      border-color: ${theme.colors.primary.light3};
    }

    &:active {
      transform: scale(0.95);
    }
  `}
`;

const CardBody = styled.div`
  ${({ theme }) => `
    padding: ${theme.gridUnit * 4}px;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: ${theme.gridUnit * 3}px;
    position: relative;
    min-height: 92px;
  `}
`;

const EntityMeta = styled.div`
  ${({ theme }) => `
    display: flex;
    flex-direction: column;
    gap: ${theme.gridUnit * 2}px;
  `}
`;

const MetaRow = styled.div`
  ${({ theme }) => `
    display: flex;
    align-items: center;
    gap: ${theme.gridUnit}px;
    font-size: ${theme.typography.sizes.s}px;
    color: ${theme.colors.grayscale.base};
  `}
`;

const MetaLabel = styled.span`
  ${({ theme }) => `
    font-weight: ${theme.typography.weights.medium};
    color: ${theme.colors.grayscale.dark1};
    min-width: 60px;
  `}
`;

const CardFooter = styled.div`
  ${({ theme }) => `
    padding: ${theme.gridUnit * 3}px ${theme.gridUnit * 4}px;
    border-top: 1px solid ${theme.colors.grayscale.light2};
    background: ${theme.colors.grayscale.light4};
    margin-top: auto;
  `}
`;

const CreatorInfo = styled.div`
  ${({ theme }) => `
    display: flex;
    align-items: center;
    gap: ${theme.gridUnit}px;
    font-size: ${theme.typography.sizes.s}px;
    color: ${theme.colors.grayscale.base};
  `}
`;

const CreatorAvatar = styled.div`
  ${({ theme }) => `
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: ${theme.colors.primary.light3};
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: ${theme.typography.sizes.xs}px;
    font-weight: ${theme.typography.weights.bold};
    color: ${theme.colors.primary.dark1};
    text-transform: uppercase;
  `}
`;

const BackgroundIcon = styled.div`
  ${({ theme }) => `
    position: absolute;
    top: 50%;
    right: ${theme.gridUnit * 8}px;
    opacity: 0.05;
    pointer-events: none;
    z-index: 0;
    color: ${theme.colors.grayscale.dark1};
    scale: 3;
  `}
`;

const OwnersContainer = styled.div`
  ${({ theme }) => `
    display: flex;
    align-items: center;
    gap: ${theme.gridUnit}px;
    margin-top: ${theme.gridUnit}px;
  `}
`;

const OwnersLabel = styled.span`
  ${({ theme }) => `
    font-size: ${theme.typography.sizes.s}px;
    font-weight: ${theme.typography.weights.medium};
    color: ${theme.colors.grayscale.dark1};
    min-width: 60px;
  `}
`;

export {
  StyledEntityCard,
  CardHeader,
  TitleContainer,
  TitleRow,
  TitleText,
  LanguageFlag,
  EntityIdContainer,
  EntityIdTag,
  CopyButton,
  CardBody,
  EntityMeta,
  MetaRow,
  MetaLabel,
  CardFooter,
  CreatorInfo,
  CreatorAvatar,
  BackgroundIcon,
  OwnersContainer,
  OwnersLabel,
};
