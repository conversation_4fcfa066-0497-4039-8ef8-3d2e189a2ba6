// DODO was here

import { t } from '@superset-ui/core';

// Helper function to get entity type display name
const getEntityTypeDisplayName = (entityType: string) => {
  switch (entityType) {
    case 'chart':
      return t('Chart');
    case 'dashboard':
      return t('Dashboard');
    case 'query':
      return t('Query');
    default:
      return t('Item');
  }
};

// Helper function to get creator initials
const getCreatorInitials = (creator: string) => {
  if (!creator) return '?';
  const names = creator.split(' ');
  if (names.length >= 2) {
    return `${names[0][0]}${names[1][0]}`;
  }
  return creator.substring(0, 2);
};

// Helper function to format date
const formatDate = (dateString: string) => {
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    if (diffDays < 365) return `${Math.ceil(diffDays / 30)} months ago`;
    return `${Math.ceil(diffDays / 365)} years ago`;
  } catch {
    return dateString;
  }
};

export { getEntityTypeDisplayName, formatDate, getCreatorInitials };
