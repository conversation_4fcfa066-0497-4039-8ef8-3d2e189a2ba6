// DODO was here

import { useState } from 'react';
import Icons from 'src/components/Icons';
import { Tooltip } from 'src/components/Tooltip';
import FacePile from 'src/components/FacePile';
import { t } from '@superset-ui/core';
import {
  StyledEntityCard,
  CardHeader,
  TitleContainer,
  TitleRow,
  TitleText,
  LanguageFlag,
  EntityIdContainer,
  EntityIdTag,
  CopyButton,
  CardBody,
  EntityMeta,
  MetaRow,
  MetaLabel,
  CardFooter,
  CreatorInfo,
  CreatorAvatar,
  BackgroundIcon,
  OwnersContainer,
  OwnersLabel,
} from './styles';
import { EntityData, EntityType } from './types';
import {
  getEntityTypeDisplayName,
  formatDate,
  getCreatorInitials,
} from './utils';

// Helper function to get the appropriate background icon
const getBackgroundIcon = (entityType: EntityType) => {
  switch (entityType) {
    case 'chart':
      return <Icons.BarChartTile iconSize="xxl" />;
    case 'dashboard':
      return <Icons.NavDashboard iconSize="xxl" />;
    case 'query':
      return <Icons.Sql iconSize="xxl" />;
    default:
      return <Icons.File iconSize="xxl" />;
  }
};

export interface EntityCardProps {
  entity: EntityData;
}

const EntityCard = ({ entity }: EntityCardProps) => {
  const entityType = entity.type as EntityType;
  const [copyTooltipVisible, setCopyTooltipVisible] = useState(false);

  const handleClick = () => {
    window.open(entity.url, '_blank');
  };

  const handleCopyUrl = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click
    try {
      // Construct full URL based on current domain
      const fullUrl = entity.url.startsWith('http')
        ? entity.url
        : `${window.location.origin}${entity.url}`;

      await navigator.clipboard.writeText(fullUrl);
      setCopyTooltipVisible(true);
      setTimeout(() => setCopyTooltipVisible(false), 2000);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to copy URL:', error);
    }
  };

  return (
    <StyledEntityCard entityType={entityType} onClick={handleClick}>
      <BackgroundIcon>{getBackgroundIcon(entityType)}</BackgroundIcon>
      <CardHeader entityType={entityType}>
        <TitleContainer>
          <TitleRow>
            {entityType === 'dashboard' && <LanguageFlag>EN</LanguageFlag>}
            <TitleText title={entity.name}>{entity.name}</TitleText>
          </TitleRow>
          {entityType === 'dashboard' && (
            <TitleRow>
              <LanguageFlag>RU</LanguageFlag>
              <TitleText title={entity.name_ru}>{entity.name_ru}</TitleText>
            </TitleRow>
          )}
        </TitleContainer>
        <EntityIdContainer>
          <EntityIdTag>#{entity.id}</EntityIdTag>
          <Tooltip
            title={copyTooltipVisible ? t('URL copied!') : t('Copy URL')}
            placement="top"
          >
            <CopyButton onClick={handleCopyUrl}>
              <Icons.Copy iconSize="xs" />
            </CopyButton>
          </Tooltip>
        </EntityIdContainer>
      </CardHeader>

      <CardBody>
        <EntityMeta>
          <MetaRow>
            <Icons.Calendar iconSize="s" />
            <MetaLabel>{t('Modified')}:</MetaLabel>
            <Tooltip title={entity.changed_on}>
              <span>{formatDate(entity.changed_on)}</span>
            </Tooltip>
          </MetaRow>

          {entityType !== 'query' &&
            entity.owners &&
            entity.owners.length > 0 && (
              <OwnersContainer>
                <Icons.User iconSize="s" />
                <OwnersLabel>{t('Owners')}:</OwnersLabel>
                <FacePile users={entity.owners} maxCount={3} />
              </OwnersContainer>
            )}
        </EntityMeta>
      </CardBody>

      <CardFooter>
        <CreatorInfo>
          <CreatorAvatar>
            {getCreatorInitials(entity.creator || t('Unknown'))}
          </CreatorAvatar>
          <span>
            {t('Click to open')}{' '}
            {getEntityTypeDisplayName(entityType).toLowerCase()}
          </span>
        </CreatorInfo>
      </CardFooter>
    </StyledEntityCard>
  );
};

export default EntityCard;
