// DODO was here
import { ReactNode } from 'react';
import EntityCard from 'src/DodoExtensions/feautures/home/<USER>';
import {
  EntityData,
  EntityType,
} from 'src/DodoExtensions/feautures/home/<USER>/types';
import HorizontalCarousel from 'src/components/HorizontalCarousel';
import { t } from '@superset-ui/core';
import {
  StyledEntitySection,
  SectionHeader,
  SectionIconWrapper,
  SectionTitleGroup,
  SectionTitle,
  SectionSubtitle,
  EmptySection,
  ShowAllCard,
  ShowAllText,
  ShowAllLink,
} from './styles';
import { getEntityIcon, getDefaultSectionInfo } from './utils';

const getEntityTypeDisplayName = (entityType: EntityType) => {
  switch (entityType) {
    case 'chart':
      return t('Charts');
    case 'dashboard':
      return t('Dashboards');
    case 'query':
      return t('Saved Queries');
    default:
      return t('Items');
  }
};

export interface EntitySectionProps {
  entityType: EntityType;
  entities: EntityData[];
  title?: string;
  subtitle?: string;
  icon?: ReactNode;
  maxItems?: number;
  viewAllUrl?: string;
  emptyMessage?: string;
}

const EntitySection = ({
  entityType,
  entities,
  title,
  subtitle,
  icon,
  maxItems = 4,
  viewAllUrl,
  emptyMessage,
}: EntitySectionProps) => {
  const defaultInfo = getDefaultSectionInfo(entityType);
  const sectionTitle = title || defaultInfo.title;
  const sectionSubtitle = subtitle || defaultInfo.subtitle;
  const sectionIcon = icon || getEntityIcon(entityType, 'xl');
  const sectionEmptyMessage = emptyMessage || defaultInfo.emptyMessage;

  const displayEntities = entities.slice(0, maxItems);
  const hasMoreItems = entities.length >= maxItems;

  if (entities.length === 0) {
    return (
      <StyledEntitySection>
        <SectionHeader entityType={entityType}>
          <SectionIconWrapper entityType={entityType}>
            {sectionIcon}
          </SectionIconWrapper>
          <SectionTitleGroup>
            <SectionTitle>{sectionTitle}</SectionTitle>
            <SectionSubtitle>{sectionSubtitle}</SectionSubtitle>
          </SectionTitleGroup>
        </SectionHeader>
        <EmptySection>{sectionEmptyMessage}</EmptySection>
      </StyledEntitySection>
    );
  }

  return (
    <StyledEntitySection>
      <SectionHeader entityType={entityType}>
        <SectionIconWrapper entityType={entityType}>
          {sectionIcon}
        </SectionIconWrapper>
        <SectionTitleGroup>
          <SectionTitle>{sectionTitle}</SectionTitle>
          <SectionSubtitle>{sectionSubtitle}</SectionSubtitle>
        </SectionTitleGroup>
      </SectionHeader>

      <HorizontalCarousel>
        {displayEntities.map(entity => (
          <EntityCard key={`${entity.type}-${entity.id}`} entity={entity} />
        ))}
        {viewAllUrl && hasMoreItems && (
          <ShowAllLink to={viewAllUrl}>
            <ShowAllCard entityType={entityType}>
              <ShowAllText>
                {t('View all')} {getEntityTypeDisplayName(entityType)} -{'>'}
              </ShowAllText>
            </ShowAllCard>
          </ShowAllLink>
        )}
      </HorizontalCarousel>
    </StyledEntitySection>
  );
};

export default EntitySection;
