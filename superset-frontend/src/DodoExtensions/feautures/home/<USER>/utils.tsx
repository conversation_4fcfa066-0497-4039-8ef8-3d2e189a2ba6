// DODO was here
import { ReactNode } from 'react';
import { t } from '@superset-ui/core';
import Icons from 'src/components/Icons';
import { EntityType } from 'src/DodoExtensions/feautures/home/<USER>/types';

// Helper function to get entity icon component
const getEntityIcon = (
  entityType: string,
  iconSize: 'xs' | 's' | 'm' | 'l' | 'xl' | 'xxl' = 'xl',
): ReactNode => {
  switch (entityType) {
    case 'chart':
      return <Icons.BarChartTile iconSize={iconSize} />;
    case 'dashboard':
      return <Icons.NavDashboard iconSize={iconSize} />;
    case 'query':
      return <Icons.Sql iconSize={iconSize} />;
    default:
      return <Icons.File iconSize={iconSize} />;
  }
};

// Helper function to get default section info
const getDefaultSectionInfo = (entityType: EntityType) => {
  switch (entityType) {
    case 'chart':
      return {
        title: t('Charts'),
        subtitle: t('Interactive visualizations and analytics'),
        emptyMessage: t('No charts created yet'),
      };
    case 'dashboard':
      return {
        title: t('Dashboards'),
        subtitle: t('Comprehensive data stories and insights'),
        emptyMessage: t('No dashboards created yet'),
      };
    case 'query':
      return {
        title: t('Saved Queries'),
        subtitle: t('Reusable SQL queries and data explorations'),
        emptyMessage: t('No saved queries created yet'),
      };
    default:
      return {
        title: t('Items'),
        subtitle: t('Content items'),
        emptyMessage: t('No items created yet'),
      };
  }
};

export { getEntityIcon, getDefaultSectionInfo };
