// DODO was here

import { styled } from '@superset-ui/core';
import Button from 'src/components/Button';

const TeamCarouselContainer = styled.div`
  ${({ theme }) => `
    margin-bottom: ${theme.gridUnit * 8}px;
  `}
`;

const TeamMainHeader = styled.div`
  ${({ theme }) => `
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: ${theme.gridUnit * 6}px;
    padding: 0 ${theme.gridUnit * 3}px;
  `}
`;

const TeamMainTitle = styled.h2`
  ${({ theme }) => `
    margin: 0;
    font-size: ${theme.typography.sizes.xl}px;
    font-weight: ${theme.typography.weights.bold};
    color: ${theme.colors.grayscale.dark2};
  `}
`;

const ViewAllButton = styled(Button)`
  ${({ theme }) => `
    font-size: ${theme.typography.sizes.s}px;
  `}
`;

export { TeamCarouselContainer, Team<PERSON>ainHeader, TeamMainT<PERSON>le, ViewAllButton };
