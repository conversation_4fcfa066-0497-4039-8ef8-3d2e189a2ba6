// DODO was here

import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { t } from '@superset-ui/core';
import { EmptyStateBig } from 'src/components/EmptyState';
import EntitySection from 'src/DodoExtensions/feautures/home/<USER>';
import { EntityData } from 'src/DodoExtensions/feautures/home/<USER>/types';
import { fetchObjectsByTagIds } from 'src/features/tags/tags';
import { TeamEntity, TeamEntitiesData } from 'src/types/TeamMembership';
import { UserWithPermissionsAndRoles } from 'src/types/bootstrapTypes';
import {
  TeamCarouselContainer,
  TeamMainHeader,
  TeamMainTitle,
  ViewAllButton,
} from './styles';

interface TeamCarouselProps {
  showThumbnails?: boolean;
  addDangerToast: (msg: string) => void;
  userId?: string | number;
}

// Helper function to convert TeamEntity to EntityData
const convertTeamEntityToEntityData = (entity: TeamEntity): EntityData => ({
  id: entity.id,
  type: entity.type,
  name: entity.name,
  name_ru: entity.name_ru,
  url: entity.url,
  changed_on: entity.changed_on,
  creator: entity.creator,
  owners: entity.owners,
});

export default function TeamCarousel({ addDangerToast }: TeamCarouselProps) {
  const [entities, setEntities] = useState<TeamEntitiesData>({
    dashboard: [],
    chart: [],
    query: [],
  });
  const [loading, setLoading] = useState(true);
  const { team } = useSelector<any, UserWithPermissionsAndRoles>(
    state => state.user,
  );

  useEffect(() => {
    if (!team?.tagId) {
      setLoading(false);
      return;
    }
    fetchObjectsByTagIds(
      {
        tagIds: [team?.tagId],
        types: null,
        limitPerType: { dashboard: 4, chart: 4, query: 4 },
      },
      (data: TeamEntity[]) => {
        // Use the same logic as AllEntities page
        const entitiesData: TeamEntitiesData = {
          dashboard: [],
          chart: [],
          query: [],
        };

        data.forEach(entity => {
          const object_type = entity.type as keyof TeamEntitiesData;
          if (object_type in entitiesData) {
            entitiesData[object_type].push(entity);
          } else {
            // eslint-disable-next-line no-console
            console.warn(
              'Unknown entity type:',
              entity.type,
              'Available types:',
              Object.keys(entitiesData),
            );
          }
        });

        // console.log('Final entitiesData:', entitiesData);
        setEntities(entitiesData);
        setLoading(false);
      },
      () => {
        addDangerToast(t('Error fetching team entities'));
        setLoading(false);
      },
    );
  }, [team?.tagId, addDangerToast]);

  const totalEntities =
    entities.dashboard.length + entities.chart.length + entities.query.length;

  if (!team?.tagId) {
    return null;
  }

  if (loading) {
    return (
      <TeamCarouselContainer>
        <TeamMainHeader>
          <TeamMainTitle>
            {t('Loading content created by your team')} <b>{team?.name}</b>...
          </TeamMainTitle>
        </TeamMainHeader>
        <div style={{ padding: '0 36px' }}>
          <div>{t('Loading...')}</div>
        </div>
      </TeamCarouselContainer>
    );
  }

  if (totalEntities === 0) {
    return (
      <TeamCarouselContainer>
        <TeamMainHeader>
          <TeamMainTitle>
            <b>{team?.name}</b> {t('has not created anything yet')}
          </TeamMainTitle>
        </TeamMainHeader>
        <EmptyStateBig
          image="dashboard.svg"
          title={t('No content yet for %s', team?.name)}
          description={t(
            'Start creating charts and dashboards to see them here',
          )}
        />
      </TeamCarouselContainer>
    );
  }

  return (
    <TeamCarouselContainer>
      <TeamMainHeader>
        <TeamMainTitle>
          <b>{team?.name}</b> {t('team content')}
        </TeamMainTitle>
        {team?.tagId && (
          <Link to={`/superset/all_entities/?id=${team.tagId}`}>
            <ViewAllButton buttonStyle="link" buttonSize="small">
              {t('View all')}{' '}
            </ViewAllButton>
          </Link>
        )}
      </TeamMainHeader>

      {/* Dashboards Section */}
      <EntitySection
        entityType="dashboard"
        entities={entities.dashboard.map(convertTeamEntityToEntityData)}
        maxItems={4}
        viewAllUrl={
          team?.tagId
            ? `/superset/all_entities/?id=${team?.tagId}&type=dashboard`
            : undefined
        }
      />

      {/* Charts Section */}
      <EntitySection
        entityType="chart"
        entities={entities.chart.map(convertTeamEntityToEntityData)}
        maxItems={4}
        viewAllUrl={
          team?.tagId
            ? `/superset/all_entities/?id=${team?.tagId}&type=chart`
            : undefined
        }
      />

      {/* Queries Section */}
      <EntitySection
        entityType="query"
        entities={entities.query.map(convertTeamEntityToEntityData)}
        maxItems={4}
        viewAllUrl={
          team?.tagId
            ? `/superset/all_entities/?id=${team?.tagId}&type=query`
            : undefined
        }
      />
    </TeamCarouselContainer>
  );
}
