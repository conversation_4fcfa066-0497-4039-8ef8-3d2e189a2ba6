/* eslint-disable react-prefer-function-component/react-prefer-function-component */
// DOD<PERSON> was here
import { Key, ReactNode, PureComponent, createRef } from 'react';
import Tabs from 'src/components/Tabs'; // DODO added 54145210
import { AntdDropdown } from 'src/components';
import { Menu } from 'src/components/Menu';
import Button from 'src/components/Button';
import { t, styled, SupersetClient } from '@superset-ui/core';
import ModalTrigger, { ModalTriggerRef } from 'src/components/ModalTrigger';
import { CssEditor as AceCssEditor } from 'src/components/AsyncAceEditor';
import DashboardElements from 'src/DodoExtensions/dashboard/components/DashboardElements';
import ChartList from '../../../DodoExtensions/dashboard/components/ChartList'; // DODO added 54145210

interface CssEditorPropsDodoExtended {
  handleApply?: (css: string) => Promise<void>; // DODO added 54145210
  onCancel?: (originalCss: string) => void; // DODO added 54145210
}
export interface CssEditorProps extends CssEditorPropsDodoExtended {
  initialCss: string;
  triggerNode: ReactNode;
  onChange: (css: string) => void;
  addDangerToast: (msg: string) => void;
}

export type CssEditorState = {
  css: string;
  templates?: Array<{
    css: string;
    label: string;
  }>;
};
const StyledWrapper = styled.div`
  ${({ theme }) => `
    .css-editor-header {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      margin-bottom: ${theme.gridUnit * 2}px;

      h5 {
        margin-top: ${theme.gridUnit}px;
      }
    }
    .css-editor {
      border: 1px solid ${theme.colors.grayscale.light1};
    }
  `}
`;

class CssEditor extends PureComponent<CssEditorProps, CssEditorState> {
  static defaultProps: Partial<CssEditorProps> = {
    initialCss: '',
    onChange: () => {},
  };

  originalCss: string; // DODO added 54145210

  modal: ModalTriggerRef; // DODO added 54145210

  constructor(props: CssEditorProps) {
    super(props);
    this.state = {
      css: props.initialCss,
    };
    this.modal = createRef() as ModalTriggerRef; // DODO added 54145210
    this.originalCss = props.initialCss; // DODO added 54145210
    this.changeCss = this.changeCss.bind(this);
    this.changeCssTemplate = this.changeCssTemplate.bind(this);
    this.handleModalClose = this.handleModalClose.bind(this); // DODO added 54145210
    this.onApply = this.onApply.bind(this); // DODO added 54145210
  }

  componentDidMount() {
    AceCssEditor.preload();

    SupersetClient.get({ endpoint: '/csstemplateasyncmodelview/api/read' })
      .then(({ json }) => {
        const templates = json.result.map(
          (row: { template_name: string; css: string }) => ({
            value: row.template_name,
            css: row.css,
            label: row.template_name,
          }),
        );

        this.setState({ templates });
      })
      .catch(() => {
        this.props.addDangerToast(
          t('An error occurred while fetching available CSS templates'),
        );
      });
  }

  changeCss(css: string) {
    this.setState({ css }, () => {
      this.props.onChange(css);
    });
  }

  changeCssTemplate(info: { key: Key }) {
    const keyAsString = String(info.key);
    this.changeCss(keyAsString);
  }

  // DODO added 54145210
  handleModalClose() {
    if (this.props.onCancel && this.state.css !== this.originalCss) {
      this.props.onCancel(this.originalCss);
    }
    this.setState({ css: this.originalCss });
  }

  renderTemplateSelector() {
    if (this.state.templates) {
      const menu = (
        <Menu onClick={this.changeCssTemplate}>
          {this.state.templates.map(template => (
            <Menu.Item key={template.css}>{template.label}</Menu.Item>
          ))}
        </Menu>
      );
      return (
        <AntdDropdown overlay={menu} placement="bottomRight">
          <Button>{t('Load a CSS template')}</Button>
        </AntdDropdown>
      );
    }
    return null;
  }

  // DODO added 54145210
  onApply() {
    if (this.props.handleApply) {
      this.originalCss = this.state.css;
      this.modal.current?.close();
      this.props.handleApply(this.state.css);
    }
  }

  render() {
    return (
      <ModalTrigger
        ref={this.modal} // DODO added 54145210
        triggerNode={this.props.triggerNode}
        modalTitle={t('CSS')}
        responsive // DODO added 54145210
        onExit={this.handleModalClose} // DODO added 54145210
        modalBody={
          <StyledWrapper>
            {/* DODO changed 54145210 */}
            <Tabs defaultActiveKey="css">
              <Tabs.TabPane tab={t('CSS Editor')} key="css">
                <div className="css-editor-header">
                  <h5>{t('Live CSS editor')}</h5>
                  {this.renderTemplateSelector()}
                </div>
                <AceCssEditor
                  className="css-editor"
                  minLines={12}
                  maxLines={30}
                  onChange={this.changeCss}
                  height="200px"
                  width="100%"
                  editorProps={{ $blockScrolling: true }}
                  enableLiveAutocompletion
                  value={this.state.css || ''}
                />
              </Tabs.TabPane>
              <Tabs.TabPane tab={t('Charts')} key="charts">
                <ChartList />
              </Tabs.TabPane>
              <Tabs.TabPane tab={t('Elements')} key="elements">
                <DashboardElements />
              </Tabs.TabPane>
            </Tabs>
          </StyledWrapper>
        }
        // DODO added 54145210
        modalFooter={
          <>
            {this.props.handleApply && (
              <Button
                buttonStyle="primary"
                onClick={this.onApply}
                disabled={this.state.css === this.originalCss}
              >
                {t('Apply')}
              </Button>
            )}
          </>
        }
      />
    );
  }
}

export default CssEditor;
