// DODO was here
/* eslint-disable no-template-curly-in-string */
const en = {
  domain: 'superset',
  locale_data: {
    superset: {
      '': {
        domain: 'superset',
        plural_forms: 'nplurals=2; plural=(n != 1)',
        lang: 'en',
      },
      Home: [''],
      'Annotation Layers': [''],
      Manage: [''],
      Databases: [''],
      Data: [''],
      Datasets: [''],
      Charts: [''],
      Dashboards: [''],
      Plugins: [''],
      'CSS Templates': [''],
      'Row level security': [''],
      Security: [''],
      'Import Dashboards': [''],
      'SQL Editor': [''],
      'SQL Lab': [''],
      'Saved Queries': [''],
      'Query History': [''],
      'Upload a CSV': [''],
      'Upload Excel': [''],
      'Action Log': [''],
      'Dashboard Emails': [''],
      'Chart Email Schedules': [''],
      Alerts: [''],
      'Alerts & Reports': [''],
      'Access requests': [''],
      'Druid Datasources': [''],
      'Druid Clusters': [''],
      'Scan New Datasources': [''],
      'Refresh Druid Metadata': [''],
      'Issue 1000 - The datasource is too large to query.': [''],
      'Issue 1001 - The database is under an unusual load.': [''],
      'Issue 1002 - The database returned an unexpected error.': [''],
      'Issue 1003 - There is a syntax error in the SQL query. Perhaps there was a misspelling or a typo.':
        [''],
      'Issue 1004 - The column was deleted or renamed in the database.': [''],
      'Issue 1005 - The table was deleted or renamed in the database.': [''],
      'Issue 1006 - One or more parameters specified in the query are missing.':
        [''],
      'Invalid certificate': [''],
      'Unsafe return type for function %(func)s: %(value_type)s': [''],
      'Error fetching team entities': [''],
      'No content yet for %s': [''],
      'Start creating charts and dashboards to see them here': [''],
      'There was an issue fetching your team memberships': [''],
      'Unsupported return value for method %(name)s': [''],
      'Unsafe template value for key %(key)s: %(value_type)s': [''],
      'Unsupported template value for key %(key)s': [''],
      'Only `SELECT` statements are allowed against this database': [''],
      'CTAS (create table as select) can only be run with a query where the last statement is a SELECT. Please make sure your query has a SELECT as its last statement. Then, try running your query again.':
        [''],
      'CVAS (create view as select) can only be run with a query with a single SELECT statement. Please make sure your query has only a SELECT statement. Then, try running your query again.':
        [''],
      'Viz is missing a datasource': [''],
      'Applied rolling window did not return any data. Please make sure the source query satisfies the minimum periods defined in the rolling window.':
        [''],
      'From date cannot be larger than to date': [''],
      'Cached value not found': [''],
      'Columns missing in datasource: %(invalid_columns)s': [''],
      'Table View': [''],
      'You cannot use [Columns] in combination with [Group By]/[Metrics]/[Percentage Metrics]. Please choose one or the other.':
        [''],
      "Pick a granularity in the Time section or uncheck 'Include Time'": [''],
      'Time Table View': [''],
      'Pick at least one metric': [''],
      "When using 'Group By' you are limited to use a single metric": [''],
      'Pivot Table': [''],
      "Please choose at least one 'Group by' field ": [''],
      'Please choose at least one metric': [''],
      "Group By' and 'Columns' can't overlap": [''],
      Treemap: [''],
      'Calendar Heatmap': [''],
      'Bubble Chart': [''],
      'Please use 3 different metric labels': [''],
      'Pick a metric for x, y and size': [''],
      'Bullet Chart': [''],
      'Pick a metric to display': [''],
      'Big Number with Trendline': [''],
      'Pick a metric!': [''],
      'Big Number': [''],
      'Time Series - Line Chart': [''],
      'Pick a time granularity for your time series': [''],
      'An enclosed time range (both start and end) must be specified when using a Time Comparison.':
        [''],
      'Time Series - Multiple Line Charts': [''],
      'Time Series - Dual Axis Line Chart': [''],
      'Pick a metric for left axis!': [''],
      'Pick a metric for right axis!': [''],
      'Please choose different metrics on left and right axis': [''],
      'Time Series - Bar Chart': [''],
      'Time Series - Period Pivot': [''],
      'Time Series - Percent Change': [''],
      'Time Series - Stacked': [''],
      Histogram: [''],
      'Must have at least one numeric column specified': [''],
      'Distribution - Bar Chart': [''],
      "Can't have overlap between Series and Breakdowns": [''],
      'Pick at least one field for [Series]': [''],
      Sunburst: [''],
      Sankey: [''],
      'Pick exactly 2 columns as [Source / Target]': [''],
      "There's a loop in your Sankey, please provide a tree. Here's a faulty link: {}":
        [''],
      'Directed Force Layout': [''],
      "Pick exactly 2 columns to 'Group By'": [''],
      'Country Map': [''],
      'World Map': [''],
      Filters: [''],
      'Invalid filter configuration, please select a column': [''],
      'Parallel Coordinates': [''],
      Heatmap: [''],
      'Horizon Charts': [''],
      Mapbox: [''],
      '[Longitude] and [Latitude] must be set': [''],
      "Must have a [Group By] column to have 'count' as the [Label]": [''],
      'Choice of [Label] must be present in [Group By]': [''],
      'Choice of [Point Radius] must be present in [Group By]': [''],
      '[Longitude] and [Latitude] columns must be present in [Group By]': [''],
      'Deck.gl - Multiple Layers': [''],
      'Bad spatial key': [''],
      'Invalid spatial point encountered: %s': [''],
      'Encountered invalid NULL spatial entry,                                        please consider filtering those out':
        [''],
      'Deck.gl - Scatter plot': [''],
      'Deck.gl - Screen Grid': [''],
      'Deck.gl - 3D Grid': [''],
      'Deck.gl - Paths': [''],
      'Deck.gl - Polygon': [''],
      'Deck.gl - 3D HEX': [''],
      'Deck.gl - GeoJSON': [''],
      'Deck.gl - Arc': [''],
      'Event flow': [''],
      'Time Series - Paired t-test': [''],
      'Time Series - Nightingale Rose Chart': [''],
      'Partition Diagram': [''],
      'Choose either fields to [Group By] and [Metrics] and/or [Percentage Metrics], or [Columns], not both':
        [''],
      'Box Plot': [''],
      'Distribution - NVD3 - Pie Chart': [''],
      iFrame: [''],
      'Deleted %(num)d annotation layer': [
        '',
        'Deleted %(num)d annotation layers',
      ],
      'All Text': [''],
      'Deleted %(num)d annotation': ['', 'Deleted %(num)d annotations'],
      'End date must be after start date': [''],
      'Short description must be unique for this layer': [''],
      'Annotations could not be deleted.': [''],
      'Annotation not found.': [''],
      'Annotation parameters are invalid.': [''],
      'Annotation could not be created.': [''],
      'Annotation could not be updated.': [''],
      'Annotation delete failed.': [''],
      'Annotation layer parameters are invalid.': [''],
      'Annotation layer could not be deleted.': [''],
      'Annotation layer could not be created.': [''],
      'Annotation layer could not be updated.': [''],
      'Annotation layer not found.': [''],
      'Annotation layer delete failed.': [''],
      'Annotation layer has associated annotations.': [''],
      'Name must be unique': [''],
      'Deleted %(num)d chart': ['', 'Deleted %(num)d charts'],
      'Request is not JSON': [''],
      'Request is incorrect: %(error)s': [''],
      '`confidence_interval` must be between 0 and 1 (exclusive)': [''],
      'lower percentile must be greater than 0 and less than 100. Must be lower than upper percentile.':
        [''],
      'upper percentile must be greater than 0 and less than 100. Must be higher than lower percentile.':
        [''],
      '`width` must be greater or equal to 0': [''],
      '`row_limit` must be greater than or equal to 1': [''],
      '`row_offset` must be greater than or equal to 0': [''],
      'There are associated alerts or reports: %s,': [''],
      'Database does not exist': [''],
      'Dashboards do not exist': [''],
      'Datasource type is required when datasource_id is given': [''],
      'Chart parameters are invalid.': [''],
      'Chart could not be created.': [''],
      'Chart could not be updated.': [''],
      'Chart could not be deleted.': [''],
      'There are associated alerts or reports': [''],
      'Changing this chart is forbidden': [''],
      'Charts could not be deleted.': [''],
      'Import chart failed for an unknown reason': [''],
      'Owners are invalid': [''],
      'Dataset does not exist': [''],
      '`operation` property of post processing object undefined': [''],
      'Unsupported post processing operation: %(operation)s': [''],
      'Adding new datasource [{}]': [''],
      'Refreshing datasource [{}]': [''],
      'Metric(s) {} must be aggregations.': [''],
      'Unsupported extraction function: ': [''],
      Columns: [''],
      'Show Druid Column': [''],
      'Add Druid Column': [''],
      'Edit Druid Column': [''],
      Column: [''],
      Type: [''],
      Datasource: [''],
      Groupable: [''],
      Filterable: [''],
      'Whether this column is exposed in the `Filters` section of the explore view.':
        [''],
      Metrics: [''],
      'Show Druid Metric': [''],
      'Add Druid Metric': [''],
      'Edit Druid Metric': [''],
      Metric: [''],
      Description: [''],
      'Verbose Name': [''],
      JSON: [''],
      'Druid Datasource': [''],
      'Warning Message': [''],
      'Show Druid Cluster': [''],
      'Add Druid Cluster': [''],
      'Edit Druid Cluster': [''],
      'Cluster Name': [''],
      'Broker Host': [''],
      'Broker Port': [''],
      'Broker Username': [''],
      'Broker Password': [''],
      'Broker Endpoint': [''],
      'Cache Timeout': [''],
      'Metadata Last Refreshed': [''],
      'Duration (in seconds) of the caching timeout for this cluster. A timeout of 0 indicates that the cache never expires. Note this defaults to the global timeout if undefined.':
        [''],
      'Druid supports basic authentication. See [auth](http://druid.io/docs/latest/design/auth.html) and druid-basic-security extension':
        [''],
      'Show Druid Datasource': [''],
      'Add Druid Datasource': [''],
      'Edit Druid Datasource': [''],
      "The list of charts associated with this table. By altering this datasource, you may change how these associated charts behave. Also note that charts need to point to a datasource, so this form will fail at saving if removing charts from a datasource. If you want to change the datasource for a chart, overwrite the chart from the 'explore view'":
        [''],
      'Timezone offset (in hours) for this datasource': [''],
      'Time expression to use as a predicate when retrieving distinct values to populate the filter component. Only applies when `Enable Filter Select` is on. If you enter `7 days ago`, the distinct list of values in the filter will be populated based on the distinct value over the past week':
        [''],
      "Whether to populate the filter's dropdown in the explore view's filter section with a list of distinct values fetched from the backend on the fly":
        [''],
      'Redirects to this endpoint when clicking on the datasource from the datasource list':
        [''],
      'Duration (in seconds) of the caching timeout for this datasource. A timeout of 0 indicates that the cache never expires. Note this defaults to the cluster timeout if undefined.':
        [''],
      'Associated Charts': [''],
      'Data Source': [''],
      Cluster: [''],
      Owners: [''],
      'Is Hidden': [''],
      'Enable Filter Select': [''],
      'Default Endpoint': [''],
      'Time Offset': [''],
      'Datasource Name': [''],
      'Fetch Values From': [''],
      'Changed By': [''],
      Modified: [''],
      'Refreshed metadata from cluster [{}]': [''],
      'Only `SELECT` statements are allowed': [''],
      'Only single queries supported': [''],
      'Error in jinja expression in fetch values predicate: %(msg)s': [''],
      'Error in jinja expression in FROM clause: %(msg)s': [''],
      'Virtual dataset query cannot consist of multiple statements': [''],
      'Virtual dataset query must be read-only': [''],
      'Error in jinja expression in RLS filters: %(msg)s': [''],
      'Datetime column not provided as part table configuration and is required by this type of chart':
        [''],
      'Empty query?': [''],
      "Metric '%(metric)s' does not exist": [''],
      'Invalid filter operation type: %(op)s': [''],
      'Error in jinja expression in WHERE clause: %(msg)s': [''],
      'Error in jinja expression in HAVING clause: %(msg)s': [''],
      'Show Column': [''],
      'Add Column': [''],
      'Edit Column': [''],
      'Whether to make this column available as a [Time Granularity] option, column has to be DATETIME or DATETIME-like':
        [''],
      'The data type that was inferred by the database. It may be necessary to input a type manually for expression-defined columns in some cases. In most case users should not need to alter this.':
        [''],
      Table: [''],
      Expression: [''],
      'Is temporal': [''],
      'Datetime Format': [''],
      'Invalid date/timestamp format': [''],
      'Show Metric': [''],
      'Add Metric': [''],
      'Edit Metric': [''],
      'SQL Expression': [''],
      'D3 Format': [''],
      Extra: [''],
      'Row level security filter': [''],
      'Show Row level security filter': [''],
      'Add Row level security filter': [''],
      'Edit Row level security filter': [''],
      'Regular filters add where clauses to queries if a user belongs to a role referenced in the filter. Base filters apply filters to all queries except the roles defined in the filter, and can be used to define what users can see if no RLS filters within a filter group apply to them.':
        [''],
      'These are the tables this filter will be applied to.': [''],
      'For regular filters, these are the roles this filter will be applied to. For base filters, these are the roles that the filter DOES NOT apply to, e.g. Admin if admin should see all data.':
        [''],
      "Filters with the same group key will be ORed together within the group, while different filter groups will be ANDed together. Undefined group keys are treated as unique groups, i.e. are not grouped together. For example, if a table has three filters, of which two are for departments Finance and Marketing (group key = 'department'), and one refers to the region Europe (group key = 'region'), the filter clause would apply the filter (department = 'Finance' OR department = 'Marketing') AND (region = 'Europe').":
        [''],
      'This is the condition that will be added to the WHERE clause. For example, to only return rows for a particular client, you might define a regular filter with the clause `client_id = 9`. To display no rows unless a user belongs to a RLS filter role, a base filter can be created with the clause `1 = 0` (always false).':
        [''],
      Tables: [''],
      Roles: [''],
      Clause: [''],
      Creator: [''],
      'Show Table': [''],
      'Import a table definition': [''],
      'Edit Table': [''],
      'Name of the table that exists in the source database': [''],
      'Schema, as used only in some databases like Postgres, Redshift and DB2':
        [''],
      'This fields acts a Superset view, meaning that Superset will run a query against this string as a subquery.':
        [''],
      'Predicate applied when fetching distinct value to populate the filter control component. Supports jinja template syntax. Applies only when `Enable Filter Select` is on.':
        [''],
      'Redirects to this endpoint when clicking on the table from the table list':
        [''],
      "Whether the table was generated by the 'Visualize' flow in SQL Lab": [
        '',
      ],
      'A set of parameters that become available in the query using Jinja templating syntax':
        [''],
      'Duration (in seconds) of the caching timeout for this table. A timeout of 0 indicates that the cache never expires. Note this defaults to the database timeout if undefined.':
        [''],
      Database: [''],
      'Last Changed': [''],
      Schema: [''],
      Offset: [''],
      'Table Name': [''],
      'Fetch Values Predicate': [''],
      'Main Datetime Column': [''],
      'SQL Lab View': [''],
      'Template parameters': [''],
      'The table was created. As part of this two-phase configuration process, you should now click the edit button by the new table to configure it.':
        [''],
      'Refresh Metadata': [''],
      'Refresh column metadata': [''],
      'Metadata refreshed for the following table(s): %(tables)s': [''],
      'The following tables added new columns: %(tables)s': [''],
      'The following tables removed columns: %(tables)s': [''],
      'The following tables update column metadata: %(tables)s': [''],
      'Unable to refresh metadata for the following table(s): %(tables)s': [''],
      'Deleted %(num)d css template': ['', 'Deleted %(num)d css templates'],
      'CSS template could not be deleted.': [''],
      'CSS template not found.': [''],
      'Deleted %(num)d dashboard': ['', 'Deleted %(num)d dashboards'],
      'Title or Slug': [''],
      'Must be unique': [''],
      'Dashboard parameters are invalid.': [''],
      'Dashboard not found.': [''],
      'Dashboard could not be created.': [''],
      'Dashboards could not be deleted.': [''],
      'Dashboard could not be updated.': [''],
      'Dashboard could not be deleted.': [''],
      'Changing this Dashboard is forbidden': [''],
      'Import dashboard failed for an unknown reason': [''],
      'No data in file': [''],
      'Table name undefined': [''],
      'Invalid connection string, a valid string usually follows: driver://user:password@database-host/database-name':
        [''],
      'SQLite database cannot be used as a data source for security reasons.': [
        '',
      ],
      'Field cannot be decoded by JSON. %(msg)s': [''],
      'The metadata_params in Extra field is not configured correctly. The key %(key)s is invalid.':
        [''],
      'Database parameters are invalid.': [''],
      'A database with the same name already exists': [''],
      'Field is required': [''],
      'Field cannot be decoded by JSON.  %{json_error}s': [''],
      'The metadata_params in Extra field is not configured correctly. The key %{key}s is invalid.':
        [''],
      'Database not found.': [''],
      'Database could not be created.': [''],
      'Database could not be updated.': [''],
      'Connection failed, please check your connection settings': [''],
      'Cannot delete a database that has tables attached': [''],
      'Database could not be deleted.': [''],
      'Stopped an unsafe database connection': [''],
      'Could not load database driver': [''],
      'Unexpected error occurred, please check your logs for details': [''],
      'Import database failed for an unknown reason': [''],
      'Could not load database driver: {}': [''],
      'Deleted %(num)d dataset': ['', 'Deleted %(num)d datasets'],
      'Null or Empty': [''],
      'Database not allowed to change': [''],
      'One or more columns do not exist': [''],
      'One or more columns are duplicated': [''],
      'One or more columns already exist': [''],
      'One or more metrics do not exist': [''],
      'One or more metrics are duplicated': [''],
      'One or more metrics already exist': [''],
      'Table [%(table_name)s] could not be found, please double check your database connection, schema, and table name':
        [''],
      'Dataset parameters are invalid.': [''],
      'Dataset could not be created.': [''],
      'Dataset could not be updated.': [''],
      'Dataset could not be deleted.': [''],
      'Dataset(s) could not be bulk deleted.': [''],
      'Changing this dataset is forbidden': [''],
      'Import dataset failed for an unknown reason': [''],
      'Unknown Presto Error': [''],
      'We can\'t seem to resolve the column "%(column_name)s" at line %(location)s.':
        [''],
      'The table "%(table_name)s" does not exist. A valid table must be used to run this query.':
        [''],
      'Deleted %(num)d saved query': ['', 'Deleted %(num)d saved queries'],
      'Saved queries could not be deleted.': [''],
      'Saved query not found.': [''],
      'Deleted %(num)d report schedule': [
        '',
        'Deleted %(num)d report schedules',
      ],
      'Alert query returned more than one row. %s rows returned': [''],
      'Alert query returned more than one column. %s columns returned': [''],
      'Dashboard does not exist': [''],
      'Chart does not exist': [''],
      'Database is required for alerts': [''],
      'Type is required': [''],
      'Choose a chart or dashboard not both': [''],
      'Report Schedule parameters are invalid.': [''],
      'Report Schedule could not be deleted.': [''],
      'Report Schedule could not be created.': [''],
      'Report Schedule could not be updated.': [''],
      'Report Schedule not found.': [''],
      'Report Schedule delete failed.': [''],
      'Report Schedule log prune failed.': [''],
      'Report Schedule execution failed when generating a screenshot.': [''],
      'Report Schedule execution got an unexpected error.': [''],
      'Report Schedule is still working, refusing to re-compute.': [''],
      'Report Schedule reached a working timeout.': [''],
      'Alert query returned more than one row.': [''],
      'Alert validator config error.': [''],
      'Alert query returned more than one column.': [''],
      'Alert query returned a non-number value.': [''],
      'Alert found an error while executing a query.': [''],
      'Alert fired during grace period.': [''],
      'Alert ended grace period.': [''],
      'Alert on grace period': [''],
      'Report Schedule sellenium user not found': [''],
      'Report Schedule state not found': [''],
      'Report schedule unexpected error': [''],
      'Changing this report is forbidden': [''],
      'An error occurred while pruning logs ': [''],
      '\n            <b><a href="%(url)s">Explore in Superset</a></b><p></p>\n            <img src="cid:%(msgid)s">\n            ':
        [''],
      '%(prefix)s %(title)s': [''],
      '\n            *%(name)s*\n\n            <%(url)s|Explore in Superset>\n            ':
        [''],
      '\n        *%(name)s*\n\n        <%(url)s|Explore in Superset>\n        ':
        [''],
      '<b><a href="%(url)s">Explore in Superset</a></b><p></p>': [''],
      '%(name)s.csv': [''],
      '\n        *%(slice_name)s*\n\n        <%(slice_url_user_friendly)s|Explore in Superset>\n        ':
        [''],
      '[Alert] %(label)s': [''],
      New: [''],
      'SQL Query': [''],
      Chart: [''],
      Dashboard: [''],
      Profile: [''],
      Info: [''],
      Logout: [''],
      Login: [''],
      'Record Count': [''],
      'No records found': [''],
      'Filter List': [''],
      Search: [''],
      Refresh: [''],
      'Import dashboards': [''],
      'Import Dashboard(s)': [''],
      File: [''],
      'Choose File': [''],
      Upload: [''],
      'No Access!': [''],
      'You do not have permissions to access the datasource(s): %(name)s.': [
        '',
      ],
      'Request Permissions': [''],
      Cancel: [''],
      Create: [''],
      'Use the edit buttom to change this field': [''],
      'Test Connection': [''],
      '[Superset] Access to the datasource %(name)s was granted': [''],
      'Unable to find such a holiday: [{}]': [''],
      'Referenced columns not available in DataFrame.': [''],
      'Column referenced by aggregate is undefined: %(column)s': [''],
      'Operator undefined for aggregator: %(name)s': [''],
      'Invalid numpy function: %(operator)s': [''],
      'Pivot operation requires at least one index': [''],
      'Pivot operation must include at least one aggregate': [''],
      'Undefined window for rolling operation': [''],
      'Invalid rolling_type: %(type)s': [''],
      'Invalid options for %(rolling_type)s: %(options)s': [''],
      'Invalid cumulative operator: %(operator)s': [''],
      'Invalid geohash string': [''],
      'Invalid longitude/latitude': [''],
      'Invalid geodetic string': [''],
      '`fbprophet` package not installed': [''],
      'Time grain missing': [''],
      'Unsupported time grain: %(time_grain)s': [''],
      'Periods must be a positive integer value': [''],
      'Confidence interval must be between 0 and 1 (exclusive)': [''],
      'DataFrame must include temporal column': [''],
      'DataFrame include at least one series': [''],
      'percentiles must be a list or tuple with two numeric values, of which the first is lower than the second value':
        [''],
      User: [''],
      'User Roles': [''],
      'Database URL': [''],
      'Roles to grant': [''],
      'Created On': [''],
      'List Observations': [''],
      'Show Observation': [''],
      'Error Message': [''],
      'Log Retentions (days)': [''],
      "A semicolon ';' delimited list of email addresses": [''],
      'How long to keep the logs around for this alert': [''],
      'Once an alert is triggered, how long, in seconds, before Superset nags you again.':
        [''],
      'A SQL statement that defines whether the alert should get triggered or not. The query is expected to return either NULL or a number value.':
        [''],
      'annotation start time or end time is required.': [''],
      'Annotation end time must be no earlier than start time.': [''],
      Annotations: [''],
      'Show Annotation': [''],
      'Add Annotation': [''],
      'Edit Annotation': [''],
      Layer: [''],
      Label: [''],
      Start: [''],
      End: [''],
      'JSON Metadata': [''],
      'Show Annotation Layer': [''],
      'Add Annotation Layer': [''],
      'Edit Annotation Layer': [''],
      Name: [''],
      'Dataset %(name)s already exists': [''],
      'Table [%{table}s] could not be found, please double check your database connection, schema, and table name, error: {}':
        [''],
      "json isn't valid": [''],
      'Export to YAML': [''],
      'Export to YAML?': [''],
      Delete: [''],
      'Delete all Really?': [''],
      'Is favorite': [''],
      'The data source seems to have been deleted': [''],
      'The user seems to have been deleted': [''],
      'Access was requested': [''],
      'The access requests seem to have been deleted': [''],
      '%(user)s was granted the role %(role)s that gives access to the %(datasource)s':
        [''],
      'Role %(r)s was extended to provide the access to the datasource %(ds)s':
        [''],
      'You have no permission to approve this request': [''],
      'Cannot import dashboard: %(db_error)s.\nMake sure to create the database before importing the dashboard.':
        [''],
      'An unknown error occurred. Please contact your Superset administrator': [
        '',
      ],
      'Error occurred when opening the chart: %(error)s': [''],
      "You don't have the rights to ": [''],
      'alter this ': [''],
      chart: [''],
      'create a ': [''],
      'Explore - %(table)s': [''],
      'Chart [{}] has been saved': [''],
      'Chart [{}] has been overwritten': [''],
      dashboard: [''],
      'Chart [{}] was added to dashboard [{}]': [''],
      'Dashboard [{}] just got created and chart [{}] was added to it': [''],
      'This dashboard was changed recently. Please reload dashboard to get latest version.':
        [''],
      'Could not load database driver: %(driver_name)s': [''],
      "Invalid connection string, a valid string usually follows:\n'DRIVER://USER:PASSWORD@DB-HOST/DATABASE-NAME'":
        [''],
      'Malformed request. slice_id or table_name and db_name arguments are expected':
        [''],
      'Chart %(id)s not found': [''],
      "Table %(table)s wasn't found in the database %(db)s": [''],
      "Can't find User '%(name)s', please ask your admin to create one.": [''],
      "Can't find DruidCluster with cluster_name = '%(name)s'": [''],
      'Data could not be deserialized. You may want to re-run the query.': [''],
      '%(validator)s was unable to check your query.\nPlease recheck your query.\nException: %(ex)s':
        [''],
      'Failed to start remote query on a worker. Tell your administrator to verify the availability of the message queue.':
        [''],
      'Query record was not created as expected.': [''],
      'The parameter %(parameters)s in your query is undefined.': [
        '',
        'The following parameters in your query are undefined: %(parameters)s.',
      ],
      "%(user)s's profile": [''],
      'Show CSS Template': [''],
      'Add CSS Template': [''],
      'Edit CSS Template': [''],
      'Template Name': [''],
      'A human-friendly name': [''],
      'Used internally to identify the plugin. Should be set to the package name from the pluginʼs package.json':
        [''],
      'A full URL pointing to the location of the built plugin (could be hosted on a CDN for example)':
        [''],
      'Custom Plugins': [''],
      'Custom Plugin': [''],
      'Add a Plugin': [''],
      'Edit Plugin': [''],
      'Schedule Email Reports for Dashboards': [''],
      'Manage Email Reports for Dashboards': [''],
      'Changed On': [''],
      Active: [''],
      Crontab: [''],
      Recipients: [''],
      'Slack Channel': [''],
      'Deliver As Group': [''],
      'Delivery Type': [''],
      'Schedule Email Reports for Charts': [''],
      'Manage Email Reports for Charts': [''],
      'Email Format': [''],
      'List Saved Query': [''],
      'Show Saved Query': [''],
      'Add Saved Query': [''],
      'Edit Saved Query': [''],
      'End Time': [''],
      'Pop Tab Link': [''],
      'Changed on': [''],
      'Could not determine datasource type': [''],
      'Could not find viz object': [''],
      'Show Chart': [''],
      'Add Chart': [''],
      'Edit Chart': [''],
      'These parameters are generated dynamically when clicking the save or overwrite button in the explore view. This JSON object is exposed here for reference and for power users who may want to alter specific parameters.':
        [''],
      'Duration (in seconds) of the caching timeout for this chart. Note this defaults to the datasource/table timeout if undefined.':
        [''],
      'Last Modified': [''],
      Parameters: [''],
      'Visualization Type': [''],
      'Show Dashboard': [''],
      'Add Dashboard': [''],
      'Edit Dashboard': [''],
      'This json object describes the positioning of the widgets in the dashboard. It is dynamically generated when adjusting the widgets size and positions by using drag & drop in the dashboard view':
        [''],
      'The CSS for individual dashboards can be altered here, or in the dashboard view where changes are immediately visible':
        [''],
      'To get a readable URL for your dashboard': [''],
      'This JSON object is generated dynamically when clicking the save or overwrite button in the dashboard view. It is exposed here for reference and for power users who may want to alter specific parameters.':
        [''],
      'Owners is a list of users who can alter the dashboard.': [''],
      'Determines whether or not this dashboard is visible in the list of all dashboards':
        [''],
      Title: [''],
      Slug: [''],
      Published: [''],
      'Position JSON': [''],
      CSS: [''],
      'Underlying Tables': [''],
      Export: [''],
      'Export dashboards?': [''],
      'Name of table to be created from csv data.': [''],
      'CSV File': [''],
      'Select a CSV file to be uploaded to a database.': [''],
      'Only the following file extensions are allowed: %(allowed_extensions)s':
        [''],
      'Specify a schema (if database flavor supports this).': [''],
      Delimiter: [''],
      'Delimiter used by CSV file (for whitespace use \\s+).': [''],
      'Table Exists': [''],
      'If table exists do one of the following: Fail (do nothing), Replace (drop and recreate table) or Append (insert data).':
        [''],
      Fail: [''],
      Replace: [''],
      Append: [''],
      'Header Row': [''],
      'Row containing the headers to use as column names (0 is first line of data). Leave empty if there is no header row.':
        [''],
      'Index Column': [''],
      'Column to use as the row labels of the dataframe. Leave empty if no index column.':
        [''],
      'Mangle Duplicate Columns': [''],
      'Specify duplicate columns as "X.0, X.1".': [''],
      'Skip Initial Space': [''],
      'Skip spaces after delimiter.': [''],
      'Skip Rows': [''],
      'Number of rows to skip at start of file.': [''],
      'Rows to Read': [''],
      'Number of rows of file to read.': [''],
      'Skip Blank Lines': [''],
      'Skip blank lines rather than interpreting them as NaN values.': [''],
      'Parse Dates': [''],
      'A comma separated list of columns that should be parsed as dates.': [''],
      'Infer Datetime Format': [''],
      'Use Pandas to interpret the datetime format automatically.': [''],
      'Decimal Character': [''],
      'Character to interpret as decimal point.': [''],
      'Dataframe Index': [''],
      'Write dataframe index as a column.': [''],
      'Column Label(s)': [''],
      'Column label for index column(s). If None is given and Dataframe Index is True, Index Names are used.':
        [''],
      'Null values': [''],
      'Json list of the values that should be treated as null. Examples: [""], ["None", "N/A"], ["nan", "null"]. Warning: Hive database supports only single value. Use [""] for empty string.':
        [''],
      'Name of table to be created from excel data.': [''],
      'Excel File': [''],
      'Select a Excel file to be uploaded to a database.': [''],
      'Sheet Name': [''],
      'Strings used for sheet names (default is the first sheet).': [''],
      'Show Database': [''],
      'Add Database': [''],
      'Edit Database': [''],
      'Expose this DB in SQL Lab': [''],
      'Operate the database in asynchronous mode, meaning  that the queries are executed on remote workers as opposed to on the web server itself. This assumes that you have a Celery worker setup as well as a results backend. Refer to the installation docs for more information.':
        [''],
      'Allow CREATE TABLE AS option in SQL Lab': [''],
      'Allow CREATE VIEW AS option in SQL Lab': [''],
      'Allow users to run non-SELECT statements (UPDATE, DELETE, CREATE, ...) in SQL Lab':
        [''],
      'When allowing CREATE TABLE AS option in SQL Lab, this option forces the table to be created in this schema':
        [''],
      'If Presto, Trino or Drill all the queries in SQL Lab are going to be executed as the currently logged on user who must have permission to run them.<br/>If Hive and hive.server2.enable.doAs is enabled, will run the queries as service account, but impersonate the currently logged on user via hive.server2.proxy.user property.':
        [''],
      'Allow SQL Lab to fetch a list of all tables and all views across all database schemas. For large data warehouse with thousands of tables, this can be expensive and put strain on the system.':
        [''],
      'Duration (in seconds) of the caching timeout for charts of this database. A timeout of 0 indicates that the cache never expires. Note this defaults to the global timeout if undefined.':
        [''],
      'If selected, please set the schemas allowed for csv upload in Extra.': [
        '',
      ],
      'Expose in SQL Lab': [''],
      'Allow CREATE TABLE AS': [''],
      'Allow CREATE VIEW AS': [''],
      'Allow DML': [''],
      'CTAS Schema': [''],
      'SQLAlchemy URI': [''],
      'Chart Cache Timeout': [''],
      'Secure Extra': [''],
      'Root certificate': [''],
      'Async Execution': [''],
      'Impersonate the logged on user': [''],
      'Allow Csv Upload': [''],
      'Allow Multi Schema Metadata Fetch': [''],
      Backend: [''],
      'Extra field cannot be decoded by JSON. %(msg)s': [''],
      "Invalid connection string, a valid string usually follows:'DRIVER://USER:PASSWORD@DB-HOST/DATABASE-NAME'<p>Example:'****************************************************'</p>":
        [''],
      'CSV to Database configuration': [''],
      'Database "%(database_name)s" schema "%(schema_name)s" is not allowed for csv uploads. Please contact your Superset Admin.':
        [''],
      'You cannot specify a namespace both in the name of the table: "%(csv_table.table)s" and in the schema field: "%(csv_table.schema)s". Please remove one':
        [''],
      'Unable to upload CSV file "%(filename)s" to table "%(table_name)s" in database "%(db_name)s". Error message: %(error_msg)s':
        [''],
      'CSV file "%(csv_filename)s" uploaded to table "%(table_name)s" in database "%(db_name)s"':
        [''],
      'Excel to Database configuration': [''],
      'Database "%(database_name)s" schema "%(schema_name)s" is not allowed for excel uploads. Please contact your Superset Admin.':
        [''],
      'You cannot specify a namespace both in the name of the table: "%(excel_table.table)s" and in the schema field: "%(excel_table.schema)s". Please remove one':
        [''],
      'Unable to upload Excel file "%(filename)s" to table "%(table_name)s" in database "%(db_name)s". Error message: %(error_msg)s':
        [''],
      'Excel file "%(excel_filename)s" uploaded to table "%(table_name)s" in database "%(db_name)s"':
        [''],
      Logs: [''],
      'Show Log': [''],
      'Add Log': [''],
      'Edit Log': [''],
      Action: [''],
      dttm: [''],
      'Add item': [''],
      "The query couldn't be loaded": [''],
      'Your query has been scheduled. To see details of your query, navigate to Saved queries':
        [''],
      'Your query could not be scheduled': [''],
      'Failed at retrieving results': [''],
      'An error occurred while storing the latest query id in the backend. Please contact your administrator if this problem persists.':
        [''],
      'Unknown error': [''],
      'Query was stopped.': [''],
      'Unable to migrate table schema state to backend. Superset will retry later. Please contact your administrator if this problem persists.':
        [''],
      'Unable to migrate query state to backend. Superset will retry later. Please contact your administrator if this problem persists.':
        [''],
      'Unable to migrate query editor state to backend. Superset will retry later. Please contact your administrator if this problem persists.':
        [''],
      'Unable to add a new tab to the backend. Please contact your administrator.':
        [''],
      'Copy of %s': [''],
      'An error occurred while setting the active tab. Please contact your administrator.':
        [''],
      'An error occurred while fetching tab state': [''],
      'An error occurred while removing tab. Please contact your administrator.':
        [''],
      'An error occurred while removing query. Please contact your administrator.':
        [''],
      'An error occurred while setting the tab database ID. Please contact your administrator.':
        [''],
      'An error occurred while setting the tab schema. Please contact your administrator.':
        [''],
      'An error occurred while setting the tab autorun. Please contact your administrator.':
        [''],
      'An error occurred while setting the tab title. Please contact your administrator.':
        [''],
      'Your query was saved': [''],
      'Your query could not be saved': [''],
      'Your query was updated': [''],
      'Your query could not be updated': [''],
      'An error occurred while storing your query in the backend. To avoid losing your changes, please save your query using the "Save Query" button.':
        [''],
      'An error occurred while setting the tab template parameters. Please contact your administrator.':
        [''],
      'An error occurred while fetching table metadata': [''],
      'An error occurred while fetching table metadata. Please contact your administrator.':
        [''],
      'An error occurred while expanding the table schema. Please contact your administrator.':
        [''],
      'An error occurred while collapsing the table schema. Please contact your administrator.':
        [''],
      'An error occurred while removing the table schema. Please contact your administrator.':
        [''],
      'Shared query': [''],
      "The datasource couldn't be loaded": [''],
      'An error occurred while creating the data source': [''],
      "SQL Lab uses your browser's local storage to store queries and results.\n Currently, you are using ${currentUsage.toFixed(\n            2,\n          )} KB out of ${LOCALSTORAGE_MAX_USAGE_KB} KB. storage space.\n To keep SQL Lab from crashing, please delete some query tabs.\n You can re-access these queries by using the Save feature before you delete the tab. Note that you will need to close other SQL Lab windows before you do this.":
        [''],
      'Estimate selected query cost': [''],
      'Estimate cost': [''],
      'Cost estimate': [''],
      'Creating a data source and creating a new tab': [''],
      'An error occurred': [''],
      'Explore the result set in the data exploration view': [''],
      Explore: [''],
      'This query took %s seconds to run, ': [''],
      'and the explore view times out at %s seconds ': [''],
      'following this flow will most likely lead to your query timing out. ': [
        '',
      ],
      'We recommend your summarize your data further before following that flow. ':
        [''],
      'If activated you can use the ': [''],
      'feature to store a summarized data set that you can then explore.': [''],
      'Column name(s) ': [''],
      'cannot be used as a column name. The column name/alias "__timestamp"\n          is reserved for the main temporal expression, and column aliases ending with\n          double underscores followed by a numeric value (e.g. "my_col__1") are reserved\n          for deduplicating duplicate column names. Please use aliases to rename the\n          invalid column names.':
        [''],
      'Raw SQL': [''],
      'Source SQL': [''],
      SQL: [''],
      'No query history yet...': [''],
      "It seems you don't have access to any database": [''],
      'An error occurred when refreshing queries': [''],
      'Filter by user': [''],
      'Filter by database': [''],
      'Query search string': [''],
      '[From]-': [''],
      '[To]-': [''],
      'Filter by status': [''],
      Edit: [''],
      'View results': [''],
      'Data preview': [''],
      'Overwrite text in the editor with a query on this table': [''],
      'Run query in a new tab': [''],
      'Remove query from log': [''],
      'An error occurred saving dataset': [''],
      '.CSV': [''],
      Clipboard: [''],
      'Filter results': [''],
      'Database error': [''],
      'was created': [''],
      'Query in a new tab': [''],
      'The query returned no data': [''],
      'Fetch data preview': [''],
      'Refetch results': [''],
      'Track job': [''],
      Stop: [''],
      'Run selection': [''],
      Run: [''],
      'Stop running (Ctrl + x)': [''],
      'Stop running (Ctrl + e)': [''],
      'Run query (Ctrl + Return)': [''],
      'Save & Explore': [''],
      'Overwrite & Explore': [''],
      Undefined: [''],
      Save: [''],
      'Save as': [''],
      'Save query': [''],
      'Save as new': [''],
      Update: [''],
      'Label for your query': [''],
      'Write a description for your query': [''],
      'Schedule query': [''],
      Schedule: [''],
      'There was an error with your request': [''],
      'Please save the query to enable sharing': [''],
      'Copy link': [''],
      'Copy query link to your clipboard': [''],
      'Save the query to copy the link': [''],
      'No stored results found, you need to re-run your query': [''],
      'Run a query to display results here': [''],
      'Preview: `%s`': [''],
      Results: [''],
      'Query history': [''],
      'Run query': [''],
      'New tab': [''],
      'Untitled query': [''],
      'Stop query': [''],
      'Schedule the query periodically': [''],
      'You must run the query successfully first': [''],
      'It appears that the number of rows in the query results displayed\n           was limited on the server side to\n           the %s limit.':
        [''],
      'CREATE TABLE AS': [''],
      'CREATE VIEW AS': [''],
      'Estimate the cost before running a query': [''],
      'Reset state': [''],
      'Enter a new title for the tab': [''],
      'Untitled Query %s': [''],
      'Close tab': [''],
      'Rename tab': [''],
      'Expand tool bar': [''],
      'Hide tool bar': [''],
      'Close all other tabs': [''],
      'Duplicate tab': [''],
      'Copy partition query to clipboard': [''],
      'latest partition:': [''],
      'Keys for table': [''],
      'View keys & indexes (%s)': [''],
      'Sort columns alphabetically': [''],
      'Original table column order': [''],
      'Copy SELECT statement to the clipboard': [''],
      'Show CREATE VIEW statement': [''],
      'CREATE VIEW statement': [''],
      'Remove table preview': [''],
      'Assign a set of parameters as': [''],
      'below (example:': [''],
      '), and they become available in your SQL (example:': [''],
      ') by using': [''],
      'Edit template parameters': [''],
      'Invalid JSON': [''],
      'Create a new chart': [''],
      'Choose a dataset': [''],
      'If the dataset you are looking for is not available in the list, follow the instructions on how to add it in the Superset tutorial.':
        [''],
      'Choose a visualization type': [''],
      'Create new chart': [''],
      'An error occurred while loading the SQL': [''],
      'Updating chart was stopped': [''],
      'An error occurred while rendering the visualization: %s': [''],
      'Network error.': [''],
      every: [''],
      'every month': [''],
      'every day of the month': [''],
      'day of the month': [''],
      'every day of the week': [''],
      'day of the week': [''],
      'every hour': [''],
      'every minute UTC': [''],
      year: [''],
      month: [''],
      week: [''],
      day: [''],
      hour: [''],
      minute: [''],
      reboot: [''],
      Every: [''],
      in: [''],
      on: [''],
      and: [''],
      at: [''],
      ':': [''],
      'minute(s) UTC': [''],
      'Invalid cron expression': [''],
      Clear: [''],
      Sunday: [''],
      Monday: [''],
      Tuesday: [''],
      Wednesday: [''],
      Thursday: [''],
      Friday: [''],
      Saturday: [''],
      January: [''],
      February: [''],
      March: [''],
      April: [''],
      May: [''],
      June: [''],
      July: [''],
      August: [''],
      September: [''],
      October: [''],
      November: [''],
      December: [''],
      SUN: [''],
      MON: [''],
      TUE: [''],
      WED: [''],
      THU: [''],
      FRI: [''],
      SAT: [''],
      JAN: [''],
      FEB: [''],
      MAR: [''],
      APR: [''],
      MAY: [''],
      JUN: [''],
      JUL: [''],
      AUG: [''],
      SEP: [''],
      OCT: [''],
      NOV: [''],
      DEC: [''],
      OK: [''],
      'Click to see difference': [''],
      Altered: [''],
      'Chart changes': [''],
      'Superset chart': [''],
      'Check out this chart in dashboard:': [''],
      'Select ...': [''],
      'Loaded data cached': [''],
      'Loaded from cache': [''],
      'Click to force-refresh': [''],
      cached: [''],
      'Certified by %s': [''],
      'Copy to clipboard': [''],
      'Copied!': [''],
      'Sorry, your browser does not support copying. Use Ctrl / Cmd + C!': [''],
      'Error while fetching schema list': [''],
      'Error while fetching database list': [''],
      'Database:': [''],
      'Select a database': [''],
      'Force refresh schema list': [''],
      'Select a schema (%s)': [''],
      'Schema:': [''],
      datasource: [''],
      schema: [''],
      delete: [''],
      'Type "%s" to confirm': [''],
      DELETE: [''],
      'Click to edit': [''],
      "You don't have the rights to alter this title.": [''],
      'Unexpected error': [''],
      'Click to favorite/unfavorite': [''],
      'An error occurred while fetching dashboards': [''],
      'Error while fetching table list': [''],
      'Select table or type table name': [''],
      'Type to search ...': [''],
      'Select table ': [''],
      'Force refresh table list': [''],
      'See table schema': [''],
      '%s%s': [''],
      'Share dashboard': [''],
      'This may be triggered by:': [''],
      'Please reach out to the Chart Owner for assistance.': [''],
      'Chart Owner: %s': [''],
      '%s Error': [''],
      'See more': [''],
      'See less': [''],
      'Copy message': [''],
      Close: [''],
      'This was triggered by:': [''],
      'Did you mean:': [''],
      '%(suggestion)s instead of "%(undefinedParameter)s?"': [''],
      'Parameter error': [''],
      'We’re having trouble loading this visualization. Queries are set to timeout after %s second.':
        [''],
      'We’re having trouble loading these results. Queries are set to timeout after %s second.':
        [''],
      'Timeout error': [''],
      'Cell content': [''],
      'The import was successful': [''],
      OVERWRITE: [''],
      Overwrite: [''],
      Import: [''],
      'Import %s': [''],
      'Last Updated %s': [''],
      '%s Selected': [''],
      'Deselect all': [''],
      '%s-%s of %s': [''],
      Settings: [''],
      About: [''],
      'SQL query': [''],
      'There is not enough space for this component. Try decreasing its width, or increasing the destination width.':
        [''],
      'Can not move top level tab into nested tabs': [''],
      'This chart has been moved to a different filter scope.': [''],
      'There was an issue fetching the favorite status of this dashboard.': [
        '',
      ],
      'There was an issue favoriting this dashboard.': [''],
      'This dashboard is now ${nowPublished}': [''],
      'You do not have permissions to edit this dashboard.': [''],
      'This dashboard was saved successfully.': [''],
      'Could not fetch all saved charts': [''],
      'Sorry there was an error fetching saved charts: ': [''],
      Visualization: [''],
      'Data source': [''],
      Added: [''],
      Components: [''],
      "Any color palette selected here will override the colors applied to this dashboard's individual charts":
        [''],
      'Color scheme': [''],
      'Load a template': [''],
      'Load a CSS template': [''],
      'Live CSS editor': [''],
      'You have unsaved changes.': [''],
      'This dashboard is currently force refreshing; the next force refresh will be in %s.':
        [''],
      'Your dashboard is too large. Please reduce the size before save it.': [
        '',
      ],
      'Discard changes': [''],
      'An error occurred while fetching available CSS templates': [''],
      'Superset dashboard': [''],
      'Check out this dashboard: ': [''],
      'Refresh dashboard': [''],
      'Set auto-refresh interval': [''],
      'Set filter mapping': [''],
      'Edit dashboard properties': [''],
      'Edit CSS': [''],
      'Download as image': [''],
      Download: [''],
      'Toggle fullscreen': [''],
      'There is no chart definition associated with this component, could it have been deleted?':
        [''],
      'Delete this container and save to remove this message.': [''],
      'An error has occurred': [''],
      'You do not have permission to edit this dashboard': [''],
      'A valid color scheme is required': [''],
      'The dashboard has been saved': [''],
      Apply: [''],
      'Dashboard properties': [''],
      'Basic information': [''],
      'URL slug': [''],
      'A readable URL for your dashboard': [''],
      Access: [''],
      'Owners is a list of users who can alter the dashboard. Searchable by name or username.':
        [''],
      Colors: [''],
      Advanced: [''],
      'JSON metadata': [''],
      'This dashboard is not published, it will not show up in the list of dashboards. Click here to publish this dashboard.':
        [''],
      'This dashboard is not published which means it will not show up in the list of dashboards. Favorite it to see it there or access it by using the URL directly.':
        [''],
      'This dashboard is published. Click to make it a draft.': [''],
      Draft: [''],
      "Don't refresh": [''],
      '10 seconds': [''],
      '30 seconds': [''],
      '1 minute': [''],
      '5 minutes': [''],
      '30 minutes': [''],
      '1 hour': [''],
      '6 hours': [''],
      '12 hours': [''],
      '24 hours': [''],
      'Refresh interval': [''],
      'Refresh frequency': [''],
      'Are you sure you want to proceed?': [''],
      'Save for this session': [''],
      'You must pick a name for the new dashboard': [''],
      'Save dashboard': [''],
      'Overwrite Dashboard [%s]': [''],
      'Save as:': [''],
      '[dashboard name]': [''],
      'also copy (duplicate) charts': [''],
      'Filter your charts': [''],
      'Annotation layers are still loading.': [''],
      'One ore more annotation layers failed loading.': [''],
      'Cached %s': [''],
      'Fetched %s': [''],
      'Minimize chart': [''],
      'Maximize chart': [''],
      'Force refresh': [''],
      'Toggle chart description': [''],
      'View chart in Explore': [''],
      'Share chart': [''],
      'Export CSV': [''],
      'Applied Filters (%d)': [''],
      'Incompatible Filters (%d)': [''],
      'Unset Filters (%d)': [''],
      'Search...': [''],
      'No filter is selected.': [''],
      'Editing 1 filter:': [''],
      'Batch editing %d filters:': [''],
      'Configure filter scopes': [''],
      'There are no filters in this dashboard.': [''],
      'Expand all': [''],
      'Collapse all': [''],
      'This markdown component has an error.': [''],
      'This markdown component has an error. Please revert your recent changes.':
        [''],
      'Delete dashboard tab?': [''],
      Divider: [''],
      Header: [''],
      Row: [''],
      Tabs: [''],
      Preview: [''],
      'Yes, cancel': [''],
      'Keep editing': [''],
      'Select parent filters': [''],
      'Reset all': [''],
      'You have removed this filter.': [''],
      'Restore filter': [''],
      'Filter name': [''],
      'Name is required': [''],
      'Datasource is required': [''],
      Field: [''],
      'Parent filter': [''],
      None: [''],
      'Apply changes instantly': [''],
      'Allow multiple selections': [''],
      'Inverse selection': [''],
      Required: [''],
      'Are you sure you want to cancel?': [''],
      'will not be saved.': [''],
      'Filter configuration and scoping': [''],
      'Add filter': [''],
      '(Removed)': [''],
      'Undo?': [''],
      Scoping: [''],
      'Apply to all panels': [''],
      'Apply to specific panels': [''],
      'Only selected panels will be affected by this filter': [''],
      'All panels with this column will be affected by this filter': [''],
      'All filters': [''],
      'All charts': [''],
      'Warning! Changing the dataset may break the chart if the metadata does not exist.':
        [''],
      'Changing the dataset may break the chart if the chart relies on columns or metadata that does not exist in the target dataset':
        [''],
      dataset: [''],
      'Change dataset': [''],
      'Warning!': [''],
      'Search / Filter': [''],
      'Physical (table or view)': [''],
      'Virtual (SQL)': [''],
      'SQL expression': [''],
      'Data type': [''],
      'Datetime format': [''],
      'The pattern of timestamp format. For strings use ': [''],
      'Python datetime string pattern': [''],
      ' expression which needs to adhere to the ': [''],
      'ISO 8601': [''],
      ' standard to ensure that the lexicographical ordering\n                      coincides with the chronological ordering. If the\n                      timestamp format does not adhere to the ISO 8601 standard\n                      you will need to define an expression and type for\n                      transforming the string into a date or timestamp. Note\n                      currently time zones are not supported. If time is stored\n                      in epoch format, put `epoch_s` or `epoch_ms`. If no pattern\n                      is specified we fall back to using the optional defaults on a per\n                      database/column name level via the extra parameter.':
        [''],
      'Is dimension': [''],
      'Is filterable': [''],
      'Modified columns: %s': [''],
      'Removed columns: %s': [''],
      'New columns added: %s': [''],
      'Metadata has been synced': [''],
      'Column name [%s] is duplicated': [''],
      'Metric name [%s] is duplicated': [''],
      'Calculated column [%s] requires an expression': [''],
      Basic: [''],
      'Default URL': [''],
      'Default URL to redirect to when accessing from the dataset list page': [
        '',
      ],
      'Autocomplete filters': [''],
      'Whether to populate autocomplete filters options': [''],
      'Autocomplete query predicate': [''],
      'When using "Autocomplete filters", this can be used to improve performance of the query fetching the values. Use this option to apply a predicate (WHERE clause) to the query selecting the distinct values from the table. Typically the intent would be to limit the scan by applying a relative time filter on a partitioned or indexed time-related field.':
        [''],
      'Extra data to specify table metadata. Currently supports certification data of the format: `{ "certification": { "certified_by": "Data Platform Team", "details": "This table is the source of truth." } }`.':
        [''],
      'Owners of the dataset': [''],
      'Cache timeout': [''],
      'The duration of time in seconds before the cache is invalidated': [''],
      'Hours offset': [''],
      Spatial: [''],
      virtual: [''],
      'Dataset name': [''],
      'When specifying SQL, the datasource acts as a view. Superset will use this statement as a subquery while grouping and filtering on the generated parent queries.':
        [''],
      'The JSON metric or post aggregation definition.': [''],
      Physical: [''],
      'The pointer to a physical table (or view). Keep in mind that the chart is associated to this Superset logical table, and this logical table points the physical table referenced here.':
        [''],
      'Click the lock to make changes.': [''],
      'Click the lock to prevent further changes.': [''],
      'D3 format': [''],
      'Warning message': [''],
      'Warning message to display in the metric selector': [''],
      'Certified by': [''],
      'Person or group that has certified this metric': [''],
      'Certification details': [''],
      'Details of the certification': [''],
      'Be careful.': [''],
      'Changing these settings will affect all charts using this dataset, including charts owned by other people.':
        [''],
      Source: [''],
      'Sync columns from source': [''],
      'Calculated columns': [''],
      'The dataset has been saved': [''],
      'The dataset configuration exposed here\n                affects all the charts using this dataset.\n                Be mindful that changing settings\n                here may affect other charts\n                in undesirable ways.':
        [''],
      'Are you sure you want to save and apply changes?': [''],
      'Confirm save': [''],
      'Edit Dataset ': [''],
      'Use legacy datasource editor': [''],
      'Time range': [''],
      'Time column': [''],
      'Time grain': [''],
      Origin: [''],
      'Time granularity': [''],
      'A reference to the [Time] configuration, taking granularity into account':
        [''],
      'Group by': [''],
      'One or many controls to group by': [''],
      'One or many metrics to display': [''],
      Dataset: [''],
      'Visualization type': [''],
      'The type of visualization to display': [''],
      'Fixed color': [''],
      'Use this to define a static color for all circles': [''],
      'Right axis metric': [''],
      'Choose a metric for right axis': [''],
      'Linear color scheme': [''],
      'Color metric': [''],
      'A metric to use for color': [''],
      'One or many controls to pivot as columns': [''],
      'Defines the origin where time buckets start, accepts natural dates as in `now`, `sunday` or `1970-01-01`':
        [''],
      'The time granularity for the visualization. Note that you can type and use simple natural language as in `10 seconds`, `1 day` or `56 weeks`':
        [''],
      'The time column for the visualization. Note that you can define arbitrary expression that return a DATETIME column in the table. Also note that the filter below is applied against this column or expression':
        [''],
      'The time granularity for the visualization. This applies a date transformation to alter your time column and defines a new time granularity. The options here are defined on a per database engine basis in the Superset source code.':
        [''],
      'Last week': [''],
      'last day': ['Last day'],
      'last week': ['Last 7 days'],
      'last month': ['Last 30 days'],
      'last quarter': ['Last 90 days'],
      'last year': ['Last 365 days'],
      'The time range for the visualization. All relative times, e.g. "Last month", "Last 7 days", "now", etc. are evaluated on the server using the server\'s local time (sans timezone). All tooltips and placeholder times are expressed in UTC (sans timezone). The timestamps are then evaluated by the database using the engine\'s local timezone. Note one can explicitly set the timezone per the ISO 8601 format if specifying either the start and/or end time.':
        [''],
      'Row limit': [''],
      'Series limit': [''],
      'Limits the number of time series that get displayed. A sub query (or an extra phase where sub queries are not supported) is applied to limit the number of time series that get fetched and displayed. This feature is useful when grouping by high cardinality dimension(s).':
        [''],
      'Sort by': [''],
      'Metric used to define the top series': [''],
      Series: [''],
      'Defines the grouping of entities. Each series is shown as a specific color on the chart and has a legend toggle':
        [''],
      Entity: [''],
      'This defines the element to be plotted on the chart': [''],
      'X Axis': [''],
      'Metric assigned to the [X] axis': [''],
      'Y Axis': [''],
      'Metric assigned to the [Y] axis': [''],
      'Bubble size': [''],
      'Y Axis Format': [''],
      'When `Calculation type` is set to "Percentage change", the Y Axis Format is forced to `.1%`':
        [''],
      'The color scheme for rendering chart': [''],
      'Color map': [''],
      description: [''],
      bolt: [''],
      'Changing this control takes effect instantly': [''],
      Customize: [''],
      'rows retrieved': [''],
      'Sorry, An error occurred': [''],
      'No data': [''],
      'View samples': [''],
      'Search Metrics & Columns': [''],
      'Showing %s of %s': [''],
      'New chart': [''],
      'Edit properties': [''],
      'View query': [''],
      'Run in SQL Lab': [''],
      Height: [''],
      Width: [''],
      'Export to .json': [''],
      'Export to .csv format': [''],
      '%s - untitled': [''],
      'Edit chart properties': [''],
      'Control labeled ': [''],
      'Open Datasource tab': [''],
      'You do not have permission to edit this chart': [''],
      'The description can be displayed as widget headers in the dashboard view. Supports markdown.':
        [''],
      Configuration: [''],
      "Duration (in seconds) of the caching timeout for this chart. Note this defaults to the dataset's timeout if undefined.":
        [''],
      'A list of users who can alter the chart. Searchable by name or username.':
        [''],
      rows: [''],
      'Limit reached': [''],
      '**Select** a dashboard OR **create** a new one': [''],
      'Please enter a chart name': [''],
      'Save chart': [''],
      'Save & go to dashboard': [''],
      'Save as new chart': [''],
      'Save (Overwrite)': [''],
      'Save as ...': [''],
      'Chart name': [''],
      'Add to dashboard': [''],
      'Display configuration': [''],
      'Configure your how you overlay is displayed here.': [''],
      Style: [''],
      Opacity: [''],
      Color: [''],
      'Line width': [''],
      'Layer configuration': [''],
      'Configure the basics of your Annotation Layer.': [''],
      Mandatory: [''],
      'Hide layer': [''],
      'Choose the annotation layer type': [''],
      'Annotation layer type': [''],
      Remove: [''],
      'Edit annotation layer': [''],
      'Add annotation layer': [''],
      '`Min` value should be numeric or empty': [''],
      '`Max` value should be numeric or empty': [''],
      Min: [''],
      Max: [''],
      'Edit dataset': [''],
      'View in SQL Lab': [''],
      'More dataset related options': [''],
      'Superset supports smart date parsing. Strings like `3 weeks ago`, `last sunday`, or `2 weeks from now` can be used.':
        [''],
      Default: [''],
      '(optional) default value for the filter, when using the multiple option, you can use a semicolon-delimited list of options.':
        [''],
      'Sort metric': [''],
      'Metric to sort the results by': [''],
      'Sort ascending': [''],
      'Check for sorting ascending': [''],
      'Multiple selections allowed, otherwise filter is limited to a single value':
        [''],
      'Search all filter options': [''],
      'By default, each filter loads at most 1000 choices at the initial page load. Check this box if you have more than 1000 filter values and want to enable dynamically searching that loads filter values as users type (may add stress to your database).':
        [''],
      'User must select a value for this filter': [''],
      'Filter configuration': [''],
      'Error while fetching data': [''],
      'No results found': [''],
      '%s option(s)': [''],
      'Invalid lat/long configuration.': [''],
      'Reverse lat/long ': [''],
      'Longitude & Latitude columns': [''],
      'Delimited long & lat single column': [''],
      'Multiple formats accepted, look the geopy.points Python library for more details':
        [''],
      Geohash: [''],
      textarea: [''],
      'in modal': [''],
      'Time series columns': [''],
      'This visualization type is not supported.': [''],
      'Click to change visualization type': [''],
      'Select a visualization type': [''],
      'Failed to verify select options: %s': [''],
      'RANGE TYPE': [''],
      'Actual time range': [''],
      CANCEL: [''],
      APPLY: [''],
      'Edit time range': [''],
      'Configure advanced time range': [''],
      START: [''],
      END: [''],
      'Configure Time Range: Previous...': [''],
      'Configure Time Range: Last...': [''],
      'Configure custom time range': [''],
      'Relative quantity': [''],
      'Anchor to': [''],
      NOW: [''],
      'Date/Time': [''],
      Simple: [''],
      'Custom SQL': [''],
      'No such column found. To filter on a metric, try the Custom SQL tab.': [
        '',
      ],
      '%s column(s) and metric(s)': [''],
      '%s column(s)': [''],
      'To filter on a metric, use Custom SQL tab.': [''],
      '%s operator(s)': [''],
      'Type a value here': [''],
      'Filter value (case sensitive)': [''],
      'choose WHERE or HAVING...': [''],
      'Filters by columns': [''],
      'Filters by metrics': [''],
      "\n                This filter was inherited from the dashboard's context.\n                It won't be saved when saving the chart.\n              ":
        [''],
      '%s aggregates(s)': [''],
      '%s saved metric(s)': [''],
      Saved: [''],
      'Saved metric': [''],
      column: [''],
      aggregate: [''],
      'My metric': [''],
      'Add metric': [''],
      Code: [''],
      'Markup type': [''],
      'Pick your favorite markup language': [''],
      'Put your code here': [''],
      Query: [''],
      URL: [''],
      "Templated link, it's possible to include {{ metric }} or other values coming from the controls.":
        [''],
      Time: [''],
      'Time related form attributes': [''],
      'Chart type': [''],
      'Chart ID': [''],
      'The id of the active chart': [''],
      'Cache Timeout (seconds)': [''],
      'The number of seconds before expiring the cache': [''],
      'URL parameters': [''],
      'Extra parameters for use in jinja templated queries': [''],
      'Time range endpoints': [''],
      'Time range endpoints (SIP-15)': [''],
      'Annotations and layers': [''],
      'Sort descending': [''],
      'Whether to sort descending or ascending': [''],
      Contribution: [''],
      'Compute the contribution to the total': [''],
      'Advanced analytics': [''],
      'This section contains options that allow for advanced analytical post processing of query results':
        [''],
      'Rolling window': [''],
      'Rolling function': [''],
      'Defines a rolling window function to apply, works along with the [Periods] text box':
        [''],
      Periods: [''],
      'Defines the size of the rolling window function, relative to the time granularity selected':
        [''],
      'Min periods': [''],
      'The minimum number of rolling periods required to show a value. For instance if you do a cumulative sum on 7 days you may want your "Min Period" to be 7, so that all data points shown are the total of 7 periods. This will hide the "ramp up" taking place over the first 7 periods':
        [''],
      'Time comparison': [''],
      'Time shift': [''],
      'Overlay one or more timeseries from a relative time period. Expects relative time deltas in natural language (example:  24 hours, 7 days, 52 weeks, 365 days). Free text is supported.':
        [''],
      'Calculation type': [''],
      'How to display time shifts: as individual lines; as the absolute difference between the main time series and each time shift; as the percentage change; or as the ratio between series and time shifts.':
        [''],
      'Python functions': [''],
      Rule: [''],
      'Pandas resample rule': [''],
      Method: [''],
      'Pandas resample method': [''],
      Favorites: [''],
      'Created content': [''],
      'Recent activity': [''],
      'Security & Access': [''],
      'No charts': [''],
      'No dashboards': [''],
      'No favorite charts yet, go click on stars!': [''],
      'No favorite dashboards yet, go click on stars!': [''],
      'Profile picture provided by Gravatar': [''],
      joined: [''],
      'id:': [''],
      'There was an error fetching your recent activity:': [''],
      'Deleted: %s': [''],
      'There was an issue deleting: %s': [''],
      'There was an issue deleting %s: %s': [''],
      report: [''],
      alert: [''],
      reports: [''],
      alerts: [''],
      'There was an issue deleting the selected %s: %s': [''],
      'Last run': [''],
      'Notification method': [''],
      'Execution log': [''],
      Actions: [''],
      'Bulk select': [''],
      'No %s yet': [''],
      'Created by': [''],
      'An error occurred while fetching created by values: %s': [''],
      Status: [''],
      '${AlertState.success}': [''],
      '${AlertState.working}': [''],
      '${AlertState.error}': [''],
      '${AlertState.noop}': [''],
      '${AlertState.grace}': [''],
      'Alerts & reports': [''],
      Reports: [''],
      'This action will permanently delete %s.': [''],
      'Delete %s?': [''],
      'Please confirm': [''],
      'Are you sure you want to delete the selected %s?': [''],
      '< (Smaller than)': [''],
      '> (Larger than)': [''],
      '<= (Smaller or equal)': [''],
      '>= (Larger or equal)': [''],
      '== (Is equal)': [''],
      '!= (Is not equal)': [''],
      'Not null': [''],
      '30 days': [''],
      '60 days': [''],
      '90 days': [''],
      'Add notification method': [''],
      'Add delivery method': [''],
      'Recipients are separated by "," or ";"': [''],
      Add: [''],
      "Edit ${isReport ? 'Report' : 'Alert'}": [''],
      "Add ${isReport ? 'Report' : 'Alert'}": [''],
      'Report name': [''],
      'Alert name': [''],
      'Alert condition': [''],
      'Trigger Alert If...': [''],
      Value: [''],
      'Report schedule': [''],
      'Alert condition schedule': [''],
      'Schedule settings': [''],
      'Log retention': [''],
      'Working timeout': [''],
      'Time in seconds': [''],
      'Grace period': [''],
      'Message content': [''],
      log: [''],
      State: [''],
      'Scheduled at': [''],
      'Start at': [''],
      Duration: [''],
      'Error message': [''],
      '${alertResource?.type}': [''],
      'CRON expression': [''],
      'Report sent': [''],
      'Alert triggered, notification sent': [''],
      'Report sending': [''],
      'Alert running': [''],
      'Report failed': [''],
      'Alert failed': [''],
      'Nothing triggered': [''],
      'Alert Triggered, In Grace Period': [''],
      '${RecipientIconName.email}': [''],
      '${RecipientIconName.slack}': [''],
      annotation: [''],
      'There was an issue deleting the selected annotations: %s': [''],
      'Edit annotation': [''],
      'Delete annotation': [''],
      Annotation: [''],
      'No annotation yet': [''],
      'Annotation Layer ${annotationLayerName}': [''],
      'Are you sure you want to delete ${annotationCurrentlyDeleting?.short_descr}?':
        [''],
      'Delete Annotation?': [''],
      'Are you sure you want to delete the selected annotations?': [''],
      'Add annotation': [''],
      'Annotation name': [''],
      date: [''],
      'Additional information': [''],
      'Description (this can be seen in the list)': [''],
      annotation_layer: [''],
      'Edit annotation layer properties': [''],
      'Annotation layer name': [''],
      'Annotation layers': [''],
      'There was an issue deleting the selected layers: %s': [''],
      'Last modified': [''],
      'Created on': [''],
      'Edit template': [''],
      'Delete template': [''],
      'Annotation layer': [''],
      'An error occurred while fetching dataset datasource values: %s': [''],
      'No annotation layers yet': [''],
      'This action will permanently delete the layer.': [''],
      'Delete Layer?': [''],
      'Are you sure you want to delete the selected layers?': [''],
      'Are you sure you want to delete': [''],
      'Last modified %s': [''],
      'The passwords for the databases below are needed in order to import them together with the charts. Please note that the "Secure Extra" and "Certificate" sections of the database configuration are not present in export files, and should be added manually after the import if they are needed.':
        [''],
      'You are importing one or more charts that already exist. Overwriting might cause you to lose some of your work. Are you sure you want to overwrite?':
        [''],
      'There was an issue deleting the selected charts: %s': [''],
      'Modified by': [''],
      Owner: [''],
      'An error occurred while fetching chart owners values: %s': [''],
      'An error occurred while fetching chart created by values: %s': [''],
      'Viz type': [''],
      'An error occurred while fetching chart dataset values: %s': [''],
      Favorite: [''],
      Yes: [''],
      No: [''],
      'Are you sure you want to delete the selected charts?': [''],
      css_template: [''],
      'Edit CSS template properties': [''],
      'Add CSS template': [''],
      'CSS template name': [''],
      css: [''],
      'CSS templates': [''],
      'There was an issue deleting the selected templates: %s': [''],
      'Last modified by %s': [''],
      'CSS template': [''],
      'This action will permanently delete the template.': [''],
      'Delete Template?': [''],
      'Are you sure you want to delete the selected templates?': [''],
      'The passwords for the databases below are needed in order to import them together with the dashboards. Please note that the "Secure Extra" and "Certificate" sections of the database configuration are not present in export files, and should be added manually after the import if they are needed.':
        [''],
      'You are importing one or more dashboards that already exist. Overwriting might cause you to lose some of your work. Are you sure you want to overwrite?':
        [''],
      'An error occurred while fetching dashboards: %s': [''],
      'There was an issue deleting the selected dashboards: ': [''],
      'An error occurred while fetching dashboard owner values: %s': [''],
      'An error occurred while fetching dashboard created by values: %s': [''],
      Unpublished: [''],
      'Are you sure you want to delete the selected dashboards?': [''],
      'Sorry, your browser does not support copying.': [''],
      'SQL Copied!': [''],
      'The passwords for the databases below are needed in order to import them. Please note that the "Secure Extra" and "Certificate" sections of the database configuration are not present in export files, and should be added manually after the import if they are needed.':
        [''],
      'You are importing one or more databases that already exist. Overwriting might cause you to lose some of your work. Are you sure you want to overwrite?':
        [''],
      database: [''],
      'An error occurred while fetching database related data: %s': [''],
      'Asynchronous query execution': [''],
      AQE: [''],
      'Allow data manipulation language': [''],
      DML: [''],
      'CSV upload': [''],
      'Delete database': [''],
      'The database %s is linked to %s charts that appear on %s dashboards. Are you sure you want to continue? Deleting the database will break those objects.':
        [''],
      'Delete Database?': [''],
      'Please enter a SQLAlchemy URI to test': [''],
      'Connection looks good!': [''],
      'ERROR: Connection failed. ': [''],
      'Sorry there was an error fetching database information: %s': [''],
      'Edit database': [''],
      'Add database': [''],
      Connection: [''],
      'Database name': [''],
      'Name your dataset': [''],
      'dialect+driver://username:password@host:port/database': [''],
      'Test connection': [''],
      'Refer to the ': [''],
      'SQLAlchemy docs': [''],
      ' for more information on how to structure your URI.': [''],
      Performance: [''],
      'Chart cache timeout': [''],
      'Operate the database in asynchronous mode, meaning that the queries are executed on remote workers as opposed to on the web server itself. This assumes that you have a Celery worker setup as well as a results backend. Refer to the installation docs for more information.':
        [''],
      'SQL Lab settings': [''],
      'Allow users to run non-SELECT statements (UPDATE, DELETE, CREATE, ...)':
        [''],
      'Allow multi schema metadata fetch': [''],
      'CTAS schema': [''],
      'When allowing CREATE TABLE AS option in SQL Lab, this option forces the table to be created in this schema.':
        [''],
      'Secure extra': [''],
      'JSON string containing additional connection configuration.': [''],
      'This is used to provide connection information for systems like Hive, Presto, and BigQuery, which do not conform to the username:password syntax normally used by SQLAlchemy.':
        [''],
      'Optional CA_BUNDLE contents to validate HTTPS requests. Only available on certain database engines.':
        [''],
      'Impersonate Logged In User (Presto, Trino, Drill & Hive)': [''],
      'If Presto, Trino or Drill all the queries in SQL Lab are going to be executed as the currently logged on user who must have permission to run them. If Hive and hive.server2.enable.doAs is enabled, will run the queries as service account, but impersonate the currently logged on user via hive.server2.proxy.user property.':
        [''],
      'Allow data upload': [''],
      'If selected, please set the schemas allowed for data upload in Extra.': [
        '',
      ],
      'JSON string containing extra configuration elements.': [''],
      '1. The engine_params object gets unpacked into the sqlalchemy.create_engine call, while the metadata_params gets unpacked into the sqlalchemy.MetaData call.':
        [''],
      '2. The metadata_cache_timeout is a cache timeout setting in seconds for metadata fetch of this database. Specify it as "metadata_cache_timeout": {"schema_cache_timeout": 600, "table_cache_timeout": 600}. If unset, cache will not be enabled for the functionality. A timeout of 0 indicates that the cache never expires.':
        [''],
      '3. The schemas_allowed_for_file_upload is a comma separated list of schemas that CSVs are allowed to upload to. Specify it as "schemas_allowed_for_file_upload": ["public", "csv_upload"]. If database flavor does not support schema or any schema is allowed to be accessed, just leave the list empty.':
        [''],
      "4. The version field is a string specifying this db's version. This should be used with Presto DBs so that the syntax is correct.":
        [''],
      '5. The allows_virtual_table_explore field is a boolean specifying whether or not the Explore button in SQL Lab results is shown.':
        [''],
      'Error while saving dataset: %s': [''],
      'Add dataset': [''],
      'The passwords for the databases below are needed in order to import them together with the datasets. Please note that the "Secure Extra" and "Certificate" sections of the database configuration are not present in export files, and should be added manually after the import if they are needed.':
        [''],
      'You are importing one or more datasets that already exist. Overwriting might cause you to lose some of your work. Are you sure you want to overwrite?':
        [''],
      'An error occurred while fetching dataset related data': [''],
      'An error occurred while fetching dataset related data: %s': [''],
      'Physical dataset': [''],
      'Virtual dataset': [''],
      'An error occurred while fetching dataset owner values: %s': [''],
      'An error occurred while fetching datasets: %s': [''],
      'An error occurred while fetching schema values: %s': [''],
      'There was an issue deleting the selected datasets: %s': [''],
      'The dataset %s is linked to %s charts that appear on %s dashboards. Are you sure you want to continue? Deleting the dataset will break those objects.':
        [''],
      'Delete Dataset?': [''],
      'Are you sure you want to delete the selected datasets?': [''],
      '0 Selected': [''],
      '%s Selected (Virtual)': [''],
      '%s Selected (Physical)': [''],
      '%s Selected (%s Physical, %s Virtual)': [''],
      'There was an issue previewing the selected query. %s': [''],
      Success: [''],
      Failed: [''],
      Running: [''],
      Offline: [''],
      Scheduled: [''],
      'Duration: %s': [''],
      'Tab name': [''],
      TABLES: [''],
      Rows: [''],
      'Open query in SQL Lab': [''],
      'An error occurred while fetching database values: %s': [''],
      'Search by query text': [''],
      'Query preview': [''],
      Previous: [''],
      Next: [''],
      'Open in SQL Lab': [''],
      'User query': [''],
      'Executed query': [''],
      'Saved queries': [''],
      'There was an issue previewing the selected query %s': [''],
      'Link Copied!': [''],
      'There was an issue deleting the selected queries: %s': [''],
      'Edit query': [''],
      'Copy query URL': [''],
      'Delete query': [''],
      'This action will permanently delete the saved query.': [''],
      'Delete Query?': [''],
      'Are you sure you want to delete the selected queries?': [''],
      'Query name': [''],
      Edited: [''],
      Created: [''],
      Viewed: [''],
      Examples: [''],
      Mine: [''],
      'Recently viewed charts, dashboards, and saved queries will appear here':
        [''],
      'Recently created charts, dashboards, and saved queries will appear here':
        [''],
      'Recent example charts, dashboards, and saved queries will appear here': [
        '',
      ],
      'Recently edited charts, dashboards, and saved queries will appear here':
        [''],
      "${tableName\n                        .split('')\n                        .slice(0, tableName.length - 1)\n                        .join('')}\n                    ":
        [''],
      "You don't have any favorites yet!": [''],
      'SQL Lab queries': [''],
      '${tableName}': [''],
      query: [''],
      Share: [''],
      'Last run %s': [''],
      Recents: [''],
      'Select start and end date': [''],
      'Type or Select [%s]': [''],
      'Filter box': [''],
      'Filters configuration': [''],
      'Filter configuration for the filter box': [''],
      'Date filter': [''],
      'Whether to include a time filter': [''],
      'Instant filtering': [''],
      'Check to apply filters instantly as they change instead of displaying [Apply] button':
        [''],
      'Show SQL granularity dropdown': [''],
      'Check to include SQL granularity dropdown': [''],
      'Show SQL time column': [''],
      'Check to include time column dropdown': [''],
      'Show Druid granularity dropdown': [''],
      'Check to include Druid granularity dropdown': [''],
      'Show Druid time origin': [''],
      'Check to include time origin dropdown': [''],
      'Limit selector values': [''],
      'These filters apply to the values available in the dropdowns': [''],
      'Time-series Table': [''],
      One_dashboard: ['dashboard'],
      Two_to_4_dashboards: ['dashboards'],
      Five_and_more_dashboards: ['dashboards'],
      Added_to: ['Added to'],
      'With space': [''],
      'With space rounded': [''],
      Used_in: ['Used in'],
      Custom: ['Exact date'],
      Second: [''],
      Minute: [''],
      '5 minute': [''],
      '10 minute': [''],
      '15 minute': [''],
      'Half hour': [''],
      Hour: [''],
      Day: [''],
      Week: [''],
      Month: [''],
      Quarter: [''],
      Year: [''],
      'Week starting sunday': [''],
      'Week starting monday': [''],
      'Week ending saturday': [''],
      'Week_ending sunday': ['Week ending Sunday'],
      'Week starting Sunday': [''],
      'Week starting Monday': [''],
      'Week ending Saturday': [''],
      'Query Mode': [''],
      Aggregate: [''],
      'Raw Records': [''],
      'Emit Filter Events': [''],
      'Show Cell Bars': [''],
      '%s option': [''],
      '%s options': [''],
      Cached: [''],
      Fetched: [''],
      'Edit chart': [''],
      'View as table': [''],
      'Export to .CSV': [''],
      'Export to Excel': [''],
      'Specific Date/Time': [''],
      'Relative Date/Time': [''],
      'You are welcome to Superset': [''],
      Onboarding: [''],
      'Select date (until included)': [''],
      'END (INCLUSIVE)': [''],
      'End date included to time range': [''],
      'Filter sets (%(filterSetCount)d)': [''],
      'New filter set': [''],
      Rebuild: [''],
      'Create filter set': [''],
      'Set as primary': [''],
      'The primary set of filters will be applied automatically': [''],
      'No global filters are currently added': [''],
      'Waiting on database...': [''],
      'Waiting on %s': [''],
      'Remove invalid filters': [''],
      'Applied filters (%s)': [''],
      'Applied filters (%d)': [''],
      'Applied cross-filters (%d)': [''],
      'Filter type': [''],
      Scope: [''],
      'Dependent on': [''],
      'Select filter': [''],
      'Select by id filter': [''],
      'Select with translation': [''],
      'Select by id with translation': [''],
      'Time filter': [''],
      'Range filter': [''],
      'Numerical range': [''],
      Current: [''],
      'Click to edit %s.': [''],
      'Use %s to open in a new tab.': [''],
      'Filter only displays values relevant to selections made in other filters.':
        [''],
      'No results were returned for this query': [''],
      'Please apply filter changes': [''],
      Summary: [''],
      'Show total aggregations of selected metrics. Note that row limit does not apply to the result.':
        [''],
      'Current day': [''],
      'Current week': [''],
      'Current month': [''],
      'Current quarter': [''],
      'Current year': [''],
      'Return to specific datetime.': [''],
      Syntax: [''],
      Example: [''],
      'Moves the given set of dates by a specified interval.': [''],
      'Truncates the specified date to the accuracy specified by the date unit.':
        [''],
      'Get the last date by the date unit.': [''],
      'Get the specify date for the holiday': [''],
      'Select All': [''],
      'Show values': [''],
      'Hide values': [''],
      'Total (%(aggregatorName)s)': [''],
      Subtotal: [''],
      Sum: [''],
      Average: [''],
      Median: [''],
      'Sample Variance': [''],
      'Sample Standard Deviation': [''],
      Minimum: [''],
      Maximum: [''],
      First: [''],
      Last: [''],
      'Sum as Fraction of Total': [''],
      'Sum as Fraction of Rows': [''],
      'Sum as Fraction of Columns': [''],
      'Count as Fraction of Total': [''],
      'Count as Fraction of Rows': [''],
      'Count as Fraction of Columns': [''],
      stream: [''],
      expand: [''],
      'Export to PDF': [''],
      'Download as Image': [''],
      'There are no components added to this tab': [''],
      'Processing file export...': [''],
      'Content with navigation': [''],
      'Reset view (Esc)': [''],
      Reset: [''],
      'Double-click to reset zoom. Use +/- keys to zoom in/out': [''],
      'Chart Navigation Features': [''],
      'This chart supports interactive navigation:': [''],
      'Pan the chart:': [''],
      Hold: [''],
      'while dragging with the mouse': [''],
      'Zoom in/out:': [''],
      'while scrolling the mouse wheel, or use': [''],
      keys: [''],
      'Reset view:': [''],
      Press: [''],
      Esc: [''],
      'or click the Reset button': [''],
      'Got it!': [''],
      'This chart applies cross-filters to charts whose datasets contain columns with the same name.':
        [''],
      'Cross-filters': [''],
      'Locate the chart': [''],
      'Your feedback will be saved anonymously': [''],
      'Your feedback will not be saved anonymously': [''],
      Anonymously: [''],
      'Leave your comments here...': [''],
      'Thank you for your feedback!': [''],
      Submit: [''],
      'To show': [''],
      'To hide': [''],
      'An error occurred while submitting your feedback. Please try again.': [
        '',
      ],
      'Please rate the dashboard on a scale of 1 to 5 on the following criteria: usability and overall performance.':
        [''],
    },
  },
};

export { en };
