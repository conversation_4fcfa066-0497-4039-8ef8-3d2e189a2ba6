// DODO was here
import moment from 'moment';
import { t, styled } from '@superset-ui/core';
import { useState, useEffect } from 'react';
import { StringParam, useQueryParam } from 'use-query-params';
import TableView, { EmptyWrapperType } from 'src/components/TableView';
import FacePile from 'src/components/FacePile';
import Tag from 'src/types/TagType';
import Owner from 'src/types/Owner';
import { EmptyStateBig } from 'src/components/EmptyState';
import EntityCard from 'src/DodoExtensions/feautures/home/<USER>';
import { EntityData } from 'src/DodoExtensions/feautures/home/<USER>/types';
import Icons from 'src/components/Icons';
import Button from 'src/components/Button';
import Pagination from 'src/components/Pagination'; // DODO added 52010498

const PAGE_SIZE = 10;

// DODO added start 52010498
const ITEMS_PER_PAGE = 12; // For card view

// View mode types
type ViewMode = 'cards' | 'table';
type ObjectType = 'dashboard' | 'chart' | 'query';

const AllEntitiesTableContainer = styled.div`
  ${({ theme }) => `
    background-color: ${theme.colors.grayscale.light4};
    min-height: 100vh;
    padding: ${theme.gridUnit * 6}px;
  `}
`;

const ViewModeToggle = styled.div`
  ${({ theme }) => `
    display: flex;
    align-items: center;
    gap: ${theme.gridUnit * 2}px;
    margin-bottom: ${theme.gridUnit * 6}px;
    padding: 0 ${theme.gridUnit * 3}px;
  `}
`;

const ViewModeButton = styled(Button)<{ isActive: boolean }>`
  ${({ theme, isActive }) => `
    display: flex;
    align-items: center;
    gap: ${theme.gridUnit}px;
    padding: ${theme.gridUnit * 2}px ${theme.gridUnit * 3}px;
    border-radius: ${theme.gridUnit}px;
    font-size: ${theme.typography.sizes.s}px;
    font-weight: ${theme.typography.weights.medium};
    transition: all 0.2s ease;

    ${
      isActive
        ? `
      background: ${theme.colors.primary.light1};
      color: ${theme.colors.grayscale.light5};
      border-color: ${theme.colors.primary.base};

      &:hover {
        background: ${theme.colors.primary.light2};
        border-color: ${theme.colors.primary.light2};
      }
    `
        : `
      background: ${theme.colors.grayscale.light5};
      color: ${theme.colors.grayscale.dark1};
      border-color: ${theme.colors.grayscale.light2};

      &:hover {
        background: ${theme.colors.grayscale.light4};
        border-color: ${theme.colors.grayscale.light1};
      }
    `
    }
  `}
`;

const EntityTypeFilter = styled.div`
  ${({ theme }) => `
    display: flex;
    align-items: center;
    gap: ${theme.gridUnit * 2}px;
    margin-bottom: ${theme.gridUnit * 6}px;
    padding: 0 ${theme.gridUnit * 3}px;
  `}
`;

const FilterButton = styled(Button)<{ isActive: boolean; entityType?: string }>`
  ${({ theme, isActive, entityType }) => `
    display: flex;
    align-items: center;
    gap: ${theme.gridUnit}px;
    padding: ${theme.gridUnit * 2}px ${theme.gridUnit * 3}px;
    border-radius: ${theme.gridUnit * 6}px;
    font-size: ${theme.typography.sizes.s}px;
    font-weight: ${theme.typography.weights.medium};
    transition: all 0.2s ease;

    ${
      isActive
        ? `
      background: ${
        entityType === 'chart'
          ? theme.colors.primary.base
          : entityType === 'dashboard'
            ? theme.colors.secondary.base
            : entityType === 'query'
              ? theme.colors.info.base
              : theme.colors.grayscale.dark1
      };
      color: ${theme.colors.grayscale.light5};
      border-color: ${
        entityType === 'chart'
          ? theme.colors.primary.base
          : entityType === 'dashboard'
            ? theme.colors.secondary.base
            : entityType === 'query'
              ? theme.colors.info.base
              : theme.colors.grayscale.dark1
      };
    `
        : `
      background: ${theme.colors.grayscale.light5};
      color: ${theme.colors.grayscale.dark1};
      border-color: ${theme.colors.grayscale.light2};

      &:hover {
        background: ${
          entityType === 'chart'
            ? theme.colors.primary.light5
            : entityType === 'dashboard'
              ? theme.colors.secondary.light5
              : entityType === 'query'
                ? theme.colors.info.light1
                : theme.colors.grayscale.light4
        };
        border-color: ${
          entityType === 'chart'
            ? theme.colors.primary.light3
            : entityType === 'dashboard'
              ? theme.colors.secondary.light3
              : entityType === 'query'
                ? theme.colors.info.light1
                : theme.colors.grayscale.light1
        };
      }
    `
    }
  `}
`;

const CardsContainer = styled.div`
  ${({ theme }) => `
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: ${theme.gridUnit * 6}px;
    padding: 0 ${theme.gridUnit * 3}px;
    margin-bottom: ${theme.gridUnit * 8}px;
  `}
`;

const SectionHeader = styled.div<{ entityType: string }>`
  ${({ theme }) => `
    display: flex;
    align-items: center;
    gap: ${theme.gridUnit * 3}px;
    padding: ${theme.gridUnit * 5}px ${theme.gridUnit * 4}px;
    box-shadow: 0 2px 8px ${theme.colors.grayscale.dark2}0F;
    position: relative;
    overflow: hidden;
  `}
`;

const SectionIconWrapper = styled.div<{ entityType: string }>`
  ${({ theme, entityType }) => `
    width: 48px;
    height: 48px;
    border-radius: ${theme.gridUnit * 2}px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: ${
      entityType === 'chart'
        ? theme.colors.primary.light4
        : entityType === 'dashboard'
          ? theme.colors.secondary.light4
          : entityType === 'query'
            ? theme.colors.info.light1
            : theme.colors.grayscale.light3
    };
    color: ${
      entityType === 'chart'
        ? theme.colors.primary.dark1
        : entityType === 'dashboard'
          ? theme.colors.secondary.dark1
          : entityType === 'query'
            ? theme.colors.info.dark1
            : theme.colors.grayscale.dark1
    };
  `}
`;

const SectionTitle = styled.h3`
  ${({ theme }) => `
    margin: 0;
    font-size: ${theme.typography.sizes.l}px;
    font-weight: ${theme.typography.weights.bold};
    color: ${theme.colors.grayscale.dark2};
  `}
`;

const SectionCount = styled.span`
  ${({ theme }) => `
    font-size: ${theme.typography.sizes.m}px;
    color: ${theme.colors.primary.base};
    font-weight: ${theme.typography.weights.medium};
    background: ${theme.colors.primary.light4};
    padding: ${theme.gridUnit}px ${theme.gridUnit * 2}px;
    border-radius: 3px;
    margin-left: ${theme.gridUnit * 2}px;
  `}
`;

const TableContainer = styled.div`
  ${({ theme }) => `
    background: ${theme.colors.grayscale.light5};
    border-radius: ${theme.gridUnit * 2}px;
    margin: 0 0 ${theme.gridUnit * 6}px;
    overflow: hidden;
    box-shadow: 0 2px 8px ${theme.colors.grayscale.dark2}1A;
    border: 1px solid ${theme.colors.grayscale.light2};

    .table {
      table-layout: fixed;
      margin: 0;
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
    }

    .table thead th {
      background: ${theme.colors.grayscale.light4};
      color: ${theme.colors.grayscale.dark2};
      font-size: ${theme.typography.sizes.s}px;
      padding: ${theme.gridUnit * 4}px ${theme.gridUnit * 3}px;
      border-bottom: 2px solid ${theme.colors.grayscale.light2};
      top: 0;
      z-index: 1;
    }

    .table tbody tr {
      transition: background-color ${theme.transitionTiming}s ease;
      border-bottom: 1px solid ${theme.colors.grayscale.light2};
    }

    .table tbody tr:hover {
      background: ${theme.colors.grayscale.light4};
    }

    .table tbody tr:last-child {
      border-bottom: none;
    }

    .td {
      width: 33%;
      padding: ${theme.gridUnit * 4}px ${theme.gridUnit * 3}px;
      vertical-align: middle;
    }

    .entity-title {
      font-family: Inter;
      font-size: ${theme.typography.sizes.m}px;
      font-weight: ${theme.typography.weights.medium};
      line-height: 1.4;
      color: ${theme.colors.grayscale.dark1};
      margin: 0;

      a {
        color: ${theme.colors.primary.base};
        text-decoration: none;
        font-weight: ${theme.typography.weights.medium};
        transition: color ${theme.transitionTiming}s ease;

        &:hover {
          color: ${theme.colors.primary.dark1};
          text-decoration: underline;
        }
      }
    }

    .table .td:first-child {
      padding-left: ${theme.gridUnit * 4}px;
    }

    .table .td:last-child {
      padding-right: ${theme.gridUnit * 4}px;
    }
  `}
`;

const PaginationContainer = styled.div`
  ${({ theme }) => `
    display: flex;
    justify-content: center;
    align-items: center;
    margin: ${theme.gridUnit * 6}px 0;
    padding: 0 ${theme.gridUnit * 3}px;
  `}
`;
// DODO added stop 52010498

interface TaggedObjectDodoExtended {
  name_ru?: string; // DODO added 52010498
}
interface TaggedObject extends TaggedObjectDodoExtended {
  id: number;
  type: string;
  name: string;
  url: string;
  changed_on: moment.MomentInput;
  created_by: number | undefined;
  creator: string;
  owners: Owner[];
  tags: Tag[];
}

export interface TaggedObjects {
  dashboard: TaggedObject[];
  chart: TaggedObject[];
  query: TaggedObject[];
}

interface AllEntitiesTableProps {
  setShowTagModal: (show: boolean) => void;
  objects: TaggedObjects;
}

// Helper function to get entity icon component
const getEntityIcon = (
  entityType: string,
  iconSize: 'xs' | 's' | 'm' | 'l' | 'xl' | 'xxl' = 'xl',
) => {
  switch (entityType) {
    case 'chart':
      return <Icons.BarChartTile iconSize={iconSize} />;
    case 'dashboard':
      return <Icons.NavDashboard iconSize={iconSize} />;
    case 'query':
      return <Icons.Sql iconSize={iconSize} />;
    default:
      return <Icons.File iconSize={iconSize} />;
  }
};

// Helper function to get entity type display name
const getEntityTypeDisplayName = (entityType: string) => {
  switch (entityType) {
    case 'chart':
      return 'Charts';
    case 'dashboard':
      return 'Dashboards';
    case 'query':
      return 'Saved Queries';
    default:
      return 'Items';
  }
};

// Helper function to convert TaggedObject to EntityData
const convertTaggedObjectToEntityData = (obj: TaggedObject): EntityData => ({
  id: obj.id,
  type: obj.type,
  name: obj.name,
  name_ru: obj.name_ru,
  url: obj.url,
  changed_on: obj.changed_on?.toString() || '',
  creator: obj.creator,
  owners: obj.owners,
});
// DODO added stop 52010498

export default function AllEntitiesTable({
  setShowTagModal,
  objects,
}: AllEntitiesTableProps) {
  // DODO added start 52010498
  const [typeFilter] = useQueryParam('type', StringParam);
  const [viewMode, setViewMode] = useState<ViewMode>('cards');
  const [activeFilter, setActiveFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);

  const showListViewObjs =
    objects.dashboard.length > 0 ||
    objects.chart.length > 0 ||
    objects.query.length > 0;

  // Set initial filter based on URL parameter
  useEffect(() => {
    if (typeFilter && ['dashboard', 'chart', 'query'].includes(typeFilter)) {
      setActiveFilter(typeFilter);
    }
  }, [typeFilter]);

  // Get all entities for filtering
  const allEntities = [
    ...objects.dashboard.map(obj => ({ ...obj, type: 'dashboard' })),
    ...objects.chart.map(obj => ({ ...obj, type: 'chart' })),
    ...objects.query.map(obj => ({ ...obj, type: 'query' })),
  ];

  // Filter entities based on active filter
  const filteredEntities =
    activeFilter === 'all'
      ? allEntities
      : allEntities.filter(entity => entity.type === activeFilter);

  // Pagination for card view
  const totalPages = Math.ceil(filteredEntities.length / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const paginatedEntities = filteredEntities.slice(
    startIndex,
    startIndex + ITEMS_PER_PAGE,
  );

  // Reset page when filter changes
  useEffect(() => {
    setCurrentPage(1);
  }, [activeFilter]);

  const renderTable = (type: ObjectType) => {
    const data = objects[type].map((o: TaggedObject) => ({
      [type]: <a href={o.url}>{o.name}</a>,
      [`${type}_ru`]: <a href={o.url}>{o.name_ru || '...'}</a>,
      modified: moment.utc(o.changed_on).fromNow(),
      owners: o.owners,
    }));

    return (
      <TableContainer>
        <SectionHeader entityType={type}>
          <SectionIconWrapper entityType={type}>
            {getEntityIcon(type, 'xl')}
          </SectionIconWrapper>
          <div style={{ flex: 1 }}>
            <SectionTitle>
              {getEntityTypeDisplayName(type)}{' '}
              <SectionCount>({objects[type].length})</SectionCount>
            </SectionTitle>
          </div>
        </SectionHeader>
        <TableView
          className="table-condensed"
          emptyWrapperType={EmptyWrapperType.Small}
          data={data}
          pageSize={PAGE_SIZE}
          columns={[
            {
              accessor: type,
              Header: `${t('Title')} (EN)`,
            },
            {
              accessor: `${type}_ru`,
              Header: `${t('Title')} (RU)`,
            },
            {
              Cell: ({
                row: {
                  original: { owners = [] },
                },
              }: any) => <FacePile users={owners} />,
              Header: t('Owners'),
              accessor: 'owners',
              disableSortBy: true,
              size: 'xl',
            },
          ]}
        />
      </TableContainer>
    );
  };

  const renderCards = () => {
    if (paginatedEntities.length === 0) {
      return (
        <EmptyStateBig
          image="dashboard.svg"
          title={t('No entities found')}
          description={t('Try adjusting your filters or create new content')}
          buttonAction={() => setShowTagModal(true)}
          buttonText={t('Add tag to entities')}
        />
      );
    }

    return (
      <>
        <CardsContainer>
          {paginatedEntities.map(entity => (
            <EntityCard
              key={`${entity.type}-${entity.id}`}
              entity={convertTaggedObjectToEntityData(entity)}
            />
          ))}
        </CardsContainer>

        {totalPages > 1 && (
          <PaginationContainer>
            <Pagination
              totalPages={totalPages}
              currentPage={currentPage}
              onChange={(page: number) => setCurrentPage(page)}
              hideFirstAndLastPageLinks
            />
          </PaginationContainer>
        )}
      </>
    );
  };
  // DODO added stop 52010498

  return (
    <AllEntitiesTableContainer>
      {/* DODO added start 52010498 */}
      {showListViewObjs ? (
        <>
          {/* View Mode Toggle */}
          <ViewModeToggle>
            <ViewModeButton
              isActive={viewMode === 'cards'}
              onClick={() => setViewMode('cards')}
            >
              <Icons.Grid iconSize="s" />
              {t('Cards')}
            </ViewModeButton>
            <ViewModeButton
              isActive={viewMode === 'table'}
              onClick={() => setViewMode('table')}
            >
              <Icons.List iconSize="s" />
              {t('Table')}
            </ViewModeButton>
          </ViewModeToggle>

          {/* Entity Type Filter */}
          <EntityTypeFilter>
            <FilterButton
              isActive={activeFilter === 'all'}
              onClick={() => setActiveFilter('all')}
            >
              <Icons.Components iconSize="s" />
              {t('All')} ({allEntities.length})
            </FilterButton>
            <FilterButton
              isActive={activeFilter === 'dashboard'}
              entityType="dashboard"
              onClick={() => setActiveFilter('dashboard')}
            >
              <Icons.NavDashboard iconSize="s" />
              {t('Dashboards')} ({objects.dashboard.length})
            </FilterButton>
            <FilterButton
              isActive={activeFilter === 'chart'}
              entityType="chart"
              onClick={() => setActiveFilter('chart')}
            >
              <Icons.BarChartTile iconSize="s" />
              {t('Charts')} ({objects.chart.length})
            </FilterButton>
            <FilterButton
              isActive={activeFilter === 'query'}
              entityType="query"
              onClick={() => setActiveFilter('query')}
            >
              <Icons.Sql iconSize="s" />
              {t('Queries')} ({objects.query.length})
            </FilterButton>
          </EntityTypeFilter>

          {/* Content */}
          {viewMode === 'cards' ? (
            renderCards()
          ) : (
            <>
              {activeFilter === 'all' || activeFilter === 'dashboard'
                ? renderTable('dashboard')
                : null}
              {activeFilter === 'all' || activeFilter === 'chart'
                ? renderTable('chart')
                : null}
              {activeFilter === 'all' || activeFilter === 'query'
                ? renderTable('query')
                : null}
            </>
          )}
        </>
      ) : (
        <EmptyStateBig
          image="dashboard.svg"
          title={t('No entities have this tag currently assigned')}
          buttonAction={() => setShowTagModal(true)}
          buttonText={t('Add tag to entities')}
        />
      )}
      {/* DODO added stop 52010498 */}
    </AllEntitiesTableContainer>
  );
}
