# DODO Features Tracking

This file maps feature names to their unique identifiers for tracking changes across the codebase.

## Feature Mapping

| # | Feature ID | Feature Name |
|---|------------|--------------|
| # | 32839638 | Team Management System |
| # | 45047288 | Homepage Cleanup
| # | 45047288 | Dataset SQL Search |
| # | 44120742 | Dashboard Title Translation |
| # | 44211759 | ID-based Filter Support |
| # | 44728892 | Metric Description Tooltips |
| # | 44728517 | Conditional Formatting |
| # | 44211751 | Filter Sets |
| # | 52010498 | Team Carousel UI On Home page |
| # | 52412004 | Fix dashboard modification on view |
---
## Usage Guidelines

1. When adding new features, assign a unique ID (format: #XXXXXXXX)
2. Update this file with the feature name and description
3. Use the feature ID in all DODO comments: `// DODO added start: Feature Name #ID`
