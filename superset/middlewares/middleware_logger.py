# DODO was here
import logging
from typing import Any, Callable

from flask import Request, Response
from flask_http_middleware import BaseHTTPMiddleware
from flask_login import current_user
from werkzeug.exceptions import NotFound

from superset.utils import json

logger = logging.getLogger(__name__)


class LoggerMiddleware(BaseHTTPMiddleware):
    def __init__(self) -> None:
        pass

    def _get_request_body(self, request: Request) -> dict[str, Any]:
        """Extract request body safely."""
        try:
            return request.get_json(silent=True) or {}
        except Exception:  # pylint: disable=broad-except
            return {}

    def _get_response_body(self, response: Response) -> dict[str, Any]:
        """Extract response body safely."""
        try:
            return json.loads(response.get_data(as_text=True)) or {}
        except Exception:  # pylint: disable=broad-except
            return {}

    def dispatch(
        self, request: Request, call_next: Callable[[Request], Response]
    ) -> Response:
        try:
            response = call_next(request)
        except NotFound as ex:
            # <PERSON>OD<PERSON> added start: Log 404 errors with detailed URL information before re-raising
            logger.error(
                "404 Not Found - URL: %s",
                request.url,
                extra={
                    "method": request.method,
                    "url": request.url,
                    "full_path": request.full_path,
                    "path": request.path,
                    "query_string": request.query_string.decode("utf-8")
                    if request.query_string
                    else "",
                    "status_code": 404,
                    "is_authenticated": current_user.is_authenticated
                    if hasattr(current_user, "is_authenticated")
                    else False,
                    "request": {
                        "body": self._get_request_body(request),
                        "args": dict(request.args),
                        "headers": dict(request.headers),
                        "referrer": request.referrer,
                        "user_agent": request.user_agent.string
                        if request.user_agent
                        else None,
                    },
                    "error_type": "NotFound",
                    "error_message": str(ex),
                },
                exc_info=False,  # No traceback for 404 errors
            )
            raise
        except Exception as ex:
            # Log any other routing/middleware exceptions with URL information
            logger.error(
                "Middleware exception - URL: %s, Error: %s",
                request.url,
                str(ex),
                extra={
                    "method": request.method,
                    "url": request.url,
                    "full_path": request.full_path,
                    "path": request.path,
                    "query_string": request.query_string.decode("utf-8")
                    if request.query_string
                    else "",
                    "is_authenticated": current_user.is_authenticated
                    if hasattr(current_user, "is_authenticated")
                    else False,
                    "request": {
                        "body": self._get_request_body(request),
                        "args": dict(request.args),
                        "headers": dict(request.headers),
                        "referrer": request.referrer,
                        "user_agent": request.user_agent.string
                        if request.user_agent
                        else None,
                    },
                    "error_type": type(ex).__name__,
                    "error_message": str(ex),
                },
                exc_info=True,
            )
            raise
            # DODO added end

        if 400 <= response.status_code < 600:
            logger.error(
                "Error response - status: %s",
                response.status_code,
                extra={
                    "method": request.method,
                    "url": request.url,
                    "status_code": response.status_code,
                    "is_authenticated": current_user.is_authenticated,
                    "request": {
                        "body": self._get_request_body(request),
                        "args": dict(request.args),
                    },
                    "response": {"body": self._get_response_body(response)},
                },
                exc_info=True,
            )

        return response
