# DODO was here
# Team Carousel UI On Home page #52010498
"""add tag_id to teams

Revision ID: a1249f0cd412
Revises: b8c8c56a3c99
Create Date: 2025-01-07 19:30:00.000000

"""

from datetime import datetime

import sqlalchemy as sa
from alembic import op
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session

from superset.migrations.shared.utils import add_column_if_not_exists
from superset.tags.models import TagType

# revision identifiers, used by Alembic.
revision = "a1249f0cd412"
down_revision = "b8c8c56a3c99"

Base = declarative_base()


class Team(Base):
    """Local Team model for migration"""

    __tablename__ = "teams"

    id = sa.Column(sa.Integer, primary_key=True)
    name = sa.Column(sa.String, nullable=False)
    is_external = sa.Column(sa.Bo<PERSON>an, nullable=False)
    slug = sa.Column(sa.String, unique=True)
    tag_id = sa.Column(sa.Integer, nullable=True)


class Tag(Base):
    """Local Tag model for migration"""

    __tablename__ = "tag"

    id = sa.Column(sa.Integer, primary_key=True)
    name = sa.Column(sa.String(250), unique=True)
    type = sa.Column(sa.Enum(TagType))
    description = sa.Column(sa.Text)
    created_on = sa.Column(sa.DateTime, default=datetime.now, nullable=True)
    changed_on = sa.Column(
        sa.DateTime, default=datetime.now, onupdate=datetime.now, nullable=True
    )


def upgrade():
    """Add tag_id column to teams table."""
    add_column_if_not_exists(
        "teams",
        sa.Column("tag_id", sa.Integer(), sa.ForeignKey("tag.id"), nullable=True),
    )

    bind = op.get_bind()
    session = Session(bind=bind)

    try:
        for team in session.query(Team).all():
            prefix_name = f"team:{team.slug}"
            tag = session.query(Tag).filter(Tag.name.in_([prefix_name, team.slug])).order_by(Tag.name.desc()).first()

            if tag:
                team.tag_id = tag.id
            else:
                tag = Tag(
                    name=prefix_name, type=TagType.team, description=f"Team tag for {prefix_name}"
                )
                session.add(tag)
                session.flush()
                team.tag_id = tag.id

            if not tag.name.startswith("team:"):
                tag.name = f"team:{tag.name}"
                session.add(tag)
                session.flush()

        session.commit()

    except Exception as e:
        print(f"Error during migration: {str(e)}")
        session.rollback()
        raise
    finally:
        session.close()


def downgrade():
    """Remove tag_id column from teams table."""
    op.drop_column("teams", "tag_id")
