import logging

from flask import request, Response
from flask_appbuilder.api import expose, protect, safe
from flask_appbuilder.models.sqla.interface import SQLAInterface
from flask_appbuilder.security.sqla.models import User
from marshmallow import ValidationError

from superset.constants import MODEL_API_RW_METHOD_PERMISSION_MAP, RouteMethod
from superset.daos.user_info import UserInfoDA<PERSON>
from superset.user.filters import (
    UserEmailFilter,
    UserIDFilter,
    UserIsActiveFilter,
    UserLastNameFilter,
    UserNameFilter,
    UserUsernameFilter,
)
from superset.user.schemas import UserSchema
from superset.views.base_api import (
    BaseSupersetModelRestApi,
)
from superset.views.users.schemas import ValidateDodoRolePutSchema

logger = logging.getLogger(__name__)


class DodoUserRestApi(BaseSupersetModelRestApi):
    """An api to get information about the user"""

    datamodel = SQLAInterface(User)

    include_route_methods = RouteMethod.REST_MODEL_VIEW_CRUD_SET | {"update_dodo_role"}
    resource_name = "dodo_user"
    allow_browser_login = True

    class_permission_name = "User"
    method_permission_name = MODEL_API_RW_METHOD_PERMISSION_MAP

    search_columns = ("id", "first_name", "last_name", "email", "username", "active")

    search_filters = {
        "id": [UserIDFilter],
        "first_name": [UserNameFilter],
        "last_name": [UserLastNameFilter],
        "email": [UserEmailFilter],
        "username": [UserUsernameFilter],
        "active": [UserIsActiveFilter],
    }

    list_columns = [
        "id",
        "username",
        "first_name",
        "last_name",
        "email",
        "is_active",
        "created_on",
        "last_login",
        "login_count",
        "teams.name",
        "user_info.country_name",
        "user_info.dodo_role",
        "user_info.is_onboarding_finished",
        "roles.name",
    ]

    order_columns = [
        "id",
        "username",
        "first_name",
        "last_name",
        "email",
        "is_active",
        "created_on",
        "last_login",
        "login_count",
    ]

    user_get_response_schema = UserSchema()

    @expose("/<int:user_id>/dodo_role", methods=("PUT",))
    @protect()
    @safe
    def update_dodo_role(self, user_id: int) -> Response:
        """Update user's DODO role by user ID
        ---
        put:
          description: >-
            Update the DODO role for a specific user (admin only)
          parameters:
          - in: path
            schema:
              type: integer
            name: user_id
            description: ID of the user to update
          requestBody:
            description: DODO role information
            required: true
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ValidateDodoRolePutSchema'
          responses:
            200:
              description: DODO role updated successfully
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      message:
                        type: string
                        example: "DODO role updated successfully"
            400:
              $ref: '#/components/responses/400'
            401:
              $ref: '#/components/responses/401'
            403:
              $ref: '#/components/responses/403'
            404:
              $ref: '#/components/responses/404'
        """
        try:
            item = ValidateDodoRolePutSchema().load(request.json)
            dodo_role = item.get("dodo_role")

            # Update the user's dodo_role
            success = UserInfoDAO.update_dodo_role(user_id, dodo_role)

            if success:
                return self.response(200, message="DODO role updated successfully")

            return self.response_404()

        except ValidationError as error:
            logger.warning("Validation error in update_dodo_role: %s", error.messages)
            return self.response_400(message=error.messages)
        except Exception as ex:  # pylint: disable=broad-except
            logger.exception("Error updating DODO role for user %s", user_id)
            return self.response_500(message=str(ex))
