import logging

from flask_appbuilder import expose
from flask_appbuilder.models.sqla.interface import SQLAInterface
from flask_appbuilder.security.decorators import has_access

from flask_appbuilder.security.sqla.models import User as UserModel

from superset.constants import MODEL_VIEW_RW_METHOD_PERMISSION_MAP, RouteMethod
from superset.superset_typing import FlaskResponse
from superset.views.base import (
    SupersetModelView,
)

logger = logging.getLogger(__name__)


class DodoUserModelView(SupersetModelView):  # pylint: disable=too-many-ancestors
    route_base = "/dodo_user"
    datamodel = SQLAInterface(UserModel)
    class_permission_name = "User"
    method_permission_name = MODEL_VIEW_RW_METHOD_PERMISSION_MAP

    include_route_methods = RouteMethod.CRUD_SET | {RouteMethod.API_READ}

    @has_access
    @expose("/list")
    def list(self) -> FlaskResponse:
        return super().render_app_template()
