# DODO was here
# pylint: disable=consider-using-transaction
from __future__ import annotations

import enum
from typing import TYPE_CHECKING

from flask_appbuilder import Model
from markupsafe import escape
from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    Enum,
    exists,
    <PERSON><PERSON><PERSON>,
    Integer,
    orm,
    String,
    Table,
    Text,
)
from sqlalchemy.engine.base import Connection
from sqlalchemy.orm import relationship, sessionmaker
from sqlalchemy.orm.mapper import Mapper
from sqlalchemy.schema import UniqueConstraint

from superset import security_manager
from superset.models.helpers import AuditMixinNullable
from superset.models.team import Team

if TYPE_CHECKING:
    from superset.connectors.sqla.models import SqlaTable
    from superset.models.core import FavStar
    from superset.models.dashboard import Dashboard
    from superset.models.slice import Slice
    from superset.models.sql_lab import Query

SessionLocal = sessionmaker()

user_favorite_tag_table = Table(
    "user_favorite_tag",
    Model.metadata,  # pylint: disable=no-member
    <PERSON><PERSON><PERSON>("user_id", <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("ab_user.id")),
    <PERSON><PERSON><PERSON>("tag_id", <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("tag.id")),
)


class TagType(enum.Enum):
    """
    Types for tags.

    Objects (queries, charts, dashboards, and datasets) will have with implicit tags based
    on metadata: types, owners and who favorited them. This way, user "alice"
    can find all their objects by querying for the tag `owner:alice`.
    """

    # pylint: disable=invalid-name
    # explicit tags, added manually by the owner
    custom = 1

    # implicit tags, generated automatically
    type = 2
    owner = 3
    favorited_by = 4
    team = 5


class ObjectType(enum.Enum):
    """Object types."""

    # pylint: disable=invalid-name
    query = 1
    chart = 2
    dashboard = 3
    dataset = 4


class Tag(Model, AuditMixinNullable):
    """A tag attached to an object (query, chart, dashboard, or dataset)."""

    __tablename__ = "tag"
    id = Column(Integer, primary_key=True)
    name = Column(String(250), unique=True)
    type = Column(Enum(TagType))
    description = Column(Text)

    objects = relationship(
        "TaggedObject", back_populates="tag", overlaps="objects,tags"
    )

    users_favorited = relationship(
        security_manager.user_model, secondary=user_favorite_tag_table
    )


class TaggedObject(Model, AuditMixinNullable):
    """An association between an object and a tag."""

    __tablename__ = "tagged_object"
    id = Column(Integer, primary_key=True)
    tag_id = Column(Integer, ForeignKey("tag.id"))
    object_id = Column(
        Integer,
        ForeignKey("dashboards.id"),
        ForeignKey("slices.id"),
        ForeignKey("saved_query.id"),
    )
    object_type = Column(Enum(ObjectType))

    tag = relationship("Tag", back_populates="objects", overlaps="tags")
    __table_args__ = (
        UniqueConstraint(
            "tag_id", "object_id", "object_type", name="uix_tagged_object"
        ),
    )

    def __str__(self) -> str:
        return f"<TaggedObject: {self.object_type}:{self.object_id} TAG:{self.tag_id}>"


def get_tag(
    name: str,
    session: orm.Session,  # pylint: disable=disallowed-name
    type_: TagType,
) -> Tag:
    tag_name = name.strip()
    tag = session.query(Tag).filter_by(name=tag_name, type=type_).one_or_none()
    if tag is None:
        tag = Tag(name=escape(tag_name), type=type_)
        session.add(tag)
        session.commit()
    return tag


def get_object_type(class_name: str) -> ObjectType:
    mapping = {
        "slice": ObjectType.chart,
        "dashboard": ObjectType.dashboard,
        "query": ObjectType.query,
        "dataset": ObjectType.dataset,
    }
    try:
        return mapping[class_name.lower()]
    except KeyError as ex:
        raise Exception(  # pylint: disable=broad-exception-raised
            f"No mapping found for {class_name}"
        ) from ex


class ObjectUpdater:
    object_type: str = "default"

    @classmethod
    def get_owners_ids(
        cls, target: Dashboard | FavStar | Slice | Query | SqlaTable
    ) -> list[int]:
        raise NotImplementedError("Subclass should implement `get_owners_ids`")

    @classmethod
    def get_owner_tag_ids(
        cls,
        session: orm.Session,  # pylint: disable=disallowed-name
        target: Dashboard | FavStar | Slice | Query | SqlaTable,
    ) -> set[int]:
        tag_ids = set()
        for owner_id in cls.get_owners_ids(target):
            name = f"owner:{owner_id}"
            tag = get_tag(name, session, TagType.owner)
            tag_ids.add(tag.id)
        return tag_ids

    @classmethod
    def _add_owners(
        cls,
        session: orm.Session,  # pylint: disable=disallowed-name
        target: Dashboard | FavStar | Slice | Query | SqlaTable,
    ) -> None:
        for owner_id in cls.get_owners_ids(target):
            name: str = f"owner:{owner_id}"
            tag = get_tag(name, session, TagType.owner)
            cls.add_tag_object_if_not_tagged(
                session, tag_id=tag.id, object_id=target.id, object_type=cls.object_type
            )

    # DODO added start: Team Carousel UI On Home page #52010498
    @classmethod
    def _add_team_tags(
        cls,
        session: orm.Session,  # pylint: disable=disallowed-name
        target: Dashboard | FavStar | Slice | Query | SqlaTable,
    ) -> None:
        """Add team tags to entities created by team members."""
        # Get the creator/owner user IDs
        owner_ids = cls.get_owners_ids(target)

        for owner_id in owner_ids:
            # Find teams that this user belongs to
            teams = (
                session.query(Team.tag_id)
                .filter(Team.participants.any(id=owner_id), Team.tag_id.isnot(None))
                .all()
            )

            for (team_tag_id,) in teams:
                if team_tag_id:
                    # Add the team tag to this entity
                    cls.add_tag_object_if_not_tagged(
                        session,
                        tag_id=team_tag_id,
                        object_id=target.id,
                        object_type=cls.object_type,
                    )

    # DODO added stop: Team Carousel UI On Home page #52010498

    @classmethod
    def add_tag_object_if_not_tagged(
        cls,
        session: orm.Session,  # pylint: disable=disallowed-name
        tag_id: int,
        object_id: int,
        object_type: str,
    ) -> None:
        # Check if the object is already tagged
        exists_query = exists().where(
            TaggedObject.tag_id == tag_id,
            TaggedObject.object_id == object_id,
            TaggedObject.object_type == object_type,
        )
        already_tagged = session.query(exists_query).scalar()

        # Add TaggedObject to the session if it isn't already tagged
        if not already_tagged:
            tagged_object = TaggedObject(
                tag_id=tag_id, object_id=object_id, object_type=object_type
            )
            session.add(tagged_object)

    @classmethod
    def after_insert(
        cls,
        _mapper: Mapper,
        connection: Connection,
        target: Dashboard | FavStar | Slice | Query | SqlaTable,
    ) -> None:
        with SessionLocal(bind=connection) as session:  # pylint: disable=disallowed-name
            # add `owner:` tags
            cls._add_owners(session, target)

            # add `type:` tags
            tag = get_tag(f"type:{cls.object_type}", session, TagType.type)
            cls.add_tag_object_if_not_tagged(
                session, tag_id=tag.id, object_id=target.id, object_type=cls.object_type
            )

            # DODO added Team Carousel UI On Home page #52010498
            cls._add_team_tags(session, target)

            session.commit()

    @classmethod
    def after_update(
        cls,
        _mapper: Mapper,
        connection: Connection,
        target: Dashboard | FavStar | Slice | Query | SqlaTable,
    ) -> None:
        with SessionLocal(bind=connection) as session:  # pylint: disable=disallowed-name
            # Fetch current owner tags
            # DODO added Team Carousel UI On Home page #52010498
            existing_owner_tags = (
                session.query(TaggedObject)
                .join(Tag)
                .filter(
                    TaggedObject.object_type == cls.object_type,
                    TaggedObject.object_id == target.id,
                    Tag.type == TagType.owner,
                )
                .all()
            )
            # DODO added Team Carousel UI On Home page #52010498
            existing_owner_tag_ids = {tag.tag_id for tag in existing_owner_tags}

            # Determine new owner IDs
            new_owner_tag_ids = cls.get_owner_tag_ids(session, target)

            # Add missing tags
            for owner_tag_id in new_owner_tag_ids - existing_owner_tag_ids:
                tagged_object = TaggedObject(
                    tag_id=owner_tag_id,
                    object_id=target.id,
                    object_type=cls.object_type,
                )
                session.add(tagged_object)

            # Remove unnecessary owner tags
            for tag in existing_owner_tags:
                if tag.tag_id not in new_owner_tag_ids:
                    session.delete(tag)

            # DODO added start: Team Carousel UI On Home page #52010498
            existing_team_tags = (
                session.query(TaggedObject)
                .join(Tag)
                .filter(
                    TaggedObject.object_type == cls.object_type,
                    TaggedObject.object_id == target.id,
                    Tag.type == TagType.team,
                )
                .all()
            )

            # Remove all existing team tags
            for tag in existing_team_tags:
                session.delete(tag)

            # Re-add current team tags
            cls._add_team_tags(session, target)
            # DODO added stop: Team Carousel UI On Home page #52010498

            session.commit()

    @classmethod
    def after_delete(
        cls,
        _mapper: Mapper,
        connection: Connection,
        target: Dashboard | FavStar | Slice | Query | SqlaTable,
    ) -> None:
        with SessionLocal(bind=connection) as session:  # pylint: disable=disallowed-name
            # delete row from `tagged_objects`
            session.query(TaggedObject).filter(
                TaggedObject.object_type == cls.object_type,
                TaggedObject.object_id == target.id,
            ).delete()

            session.commit()


class ChartUpdater(ObjectUpdater):
    object_type = "chart"

    @classmethod
    def get_owners_ids(cls, target: Slice) -> list[int]:
        return [owner.id for owner in target.owners]


class DashboardUpdater(ObjectUpdater):
    object_type = "dashboard"

    @classmethod
    def get_owners_ids(cls, target: Dashboard) -> list[int]:
        return [owner.id for owner in target.owners]


class QueryUpdater(ObjectUpdater):
    object_type = "query"

    @classmethod
    def get_owners_ids(cls, target: Query) -> list[int]:
        return [target.user_id]


class DatasetUpdater(ObjectUpdater):
    object_type = "dataset"

    @classmethod
    def get_owners_ids(cls, target: SqlaTable) -> list[int]:
        return [owner.id for owner in target.owners]


# DODO added start: Team Carousel UI On Home page #52010498
class TeamUpdater(ObjectUpdater):
    object_type = "team"

    @classmethod
    def get_owners_ids(cls, target: Team) -> list[int]:
        # Teams don't have traditional owners, but we can use participants
        # or return empty list if no owner concept applies
        return []

    @classmethod
    def after_insert(
        cls,
        _mapper: Mapper,
        connection: Connection,
        target: Team,
    ) -> None:
        with SessionLocal(bind=connection) as session:  # pylint: disable=disallowed-name
            # Create a team-specific tag with the team name
            team_tag_name = f"team:{target.slug}"
            team_tag = get_tag(team_tag_name, session, TagType.team)

            # Update the team's tag_id field using direct attribute assignment
            if team_obj := session.query(Team).filter(Team.id == target.id).first():
                team_obj.tag_id = team_tag.id

            session.commit()

    @classmethod
    def after_update(
        cls,
        _mapper: Mapper,
        connection: Connection,
        target: Team,
    ) -> None:
        with SessionLocal(bind=connection) as session:  # pylint: disable=disallowed-name
            # If team name changed, update the tag name
            if (
                hasattr(target, "_sa_instance_state")
                and target._sa_instance_state.committed_state
            ):
                old_name = target._sa_instance_state.committed_state.get("name")
                if old_name and old_name != target.slug:
                    # Update existing tag name
                    if target.tag_id:
                        tag_obj = (
                            session.query(Tag).filter(Tag.id == target.tag_id).first()
                        )
                        if tag_obj:
                            tag_obj.name = f"team:{target.slug}"
                    else:
                        # Create new tag if none exists
                        team_tag_name = f"team:{target.slug}"
                        team_tag = get_tag(team_tag_name, session, TagType.team)
                        team_obj = (
                            session.query(Team).filter(Team.id == target.id).first()
                        )
                        if team_obj:
                            team_obj.tag_id = team_tag.id

            session.commit()

    @classmethod
    def after_delete(
        cls,
        _mapper: Mapper,
        connection: Connection,
        target: Team,
    ) -> None:
        with SessionLocal(bind=connection) as session:  # pylint: disable=disallowed-name
            # Clean up the team tag when team is deleted
            if target.tag_id:
                session.query(Tag).filter(Tag.id == target.tag_id).delete()

            session.commit()

    # DODO added stop: Team Carousel UI On Home page #52010498


class FavStarUpdater:
    @classmethod
    def after_insert(
        cls, _mapper: Mapper, connection: Connection, target: FavStar
    ) -> None:
        with SessionLocal(bind=connection) as session:  # pylint: disable=disallowed-name
            name = f"favorited_by:{target.user_id}"
            tag = get_tag(name, session, TagType.favorited_by)
            tagged_object = TaggedObject(
                tag_id=tag.id,
                object_id=target.obj_id,
                object_type=get_object_type(target.class_name),
            )
            session.add(tagged_object)
            session.commit()

    @classmethod
    def after_delete(
        cls, _mapper: Mapper, connection: Connection, target: FavStar
    ) -> None:
        with SessionLocal(bind=connection) as session:  # pylint: disable=disallowed-name
            name = f"favorited_by:{target.user_id}"
            query = (
                session.query(TaggedObject.id)
                .join(Tag)
                .filter(
                    TaggedObject.object_id == target.obj_id,
                    Tag.type == TagType.favorited_by,
                    Tag.name == name,
                )
            )
            ids = [row[0] for row in query]
            session.query(TaggedObject).filter(TaggedObject.id.in_(ids)).delete(
                synchronize_session=False
            )

            session.commit()
